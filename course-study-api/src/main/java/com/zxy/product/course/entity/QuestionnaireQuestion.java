package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.QuestionnaireQuestionEntity;

/**
 * 调查问卷问题字典表
 *
 * <AUTHOR>
 */
public class QuestionnaireQuestion extends QuestionnaireQuestionEntity {

    /**
     * 等级 1-课程师资评价
     */
    public static final Integer QUESTION_LEVEL_COURSE = 1;
    /**
     * 等级 2-总体评估
     */
    public static final Integer QUESTION_LEVEL_TOTAL = 2;

    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_NOTHING = 0;
    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_COURSE_CONTENT = 1;
    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_LEARNING_EXPERIENCE = 2;
    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_TEACHING_ORGANIZATION = 3;
    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_INSTRUCTOR_LEVEL = 4;
    /**
     * 分组 0-无分组 1-课程内容 2-学习体验 3-教学组织 4-讲师水平 5-培训组织
     */
    public static final Integer QUESTION_GROUP_TRAINING_ORGANIZATION = 5;

    /**
     * 问题类型 1-单选题 2-多选题 3-问答题
     */
    public static final Integer QUESTION_TYPE_SINGLE_CHOICE = 1;
    /**
     * 问题类型 1-单选题 2-多选题 3-问答题
     */
    public static final Integer QUESTION_TYPE_MULTIPLE_CHOICE = 2;
    /**
     * 问题类型 1-单选题 2-多选题 3-问答题
     */
    public static final Integer QUESTION_TYPE_QUESTION = 3;
    private static final long serialVersionUID = -6569849040929839794L;

    /**
     * 问卷标题
     */
    private String title;

    /**
     * 问卷注意事项
     */
    private String warning;

    /**
     * 是否必填
     */
    private Integer require;

    /**
     * 是否循环
     */
    private Integer circleFlag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 答案
     */
    private String answer;

    /**
     * 调查问卷id
     */
    private String questionnaireId;

    /**
     * 用户id
     */
    private String memberId;

    /**
     * 用户名
     */
    private String memberFullName;

    /**
     * 用户手机号
     */
    private String memberPhone;

    /**
     * 用户部门
     */
    private String memberOrganizationName;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getWarning() {
        return warning;
    }

    public void setWarning(String warning) {
        this.warning = warning;
    }

    public Integer getRequire() {
        return require;
    }

    public void setRequire(Integer require) {
        this.require = require;
    }

    public Integer getCircleFlag() {
        return circleFlag;
    }

    public void setCircleFlag(Integer circleFlag) {
        this.circleFlag = circleFlag;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(String questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    public String getMemberPhone() {
        return memberPhone;
    }

    public void setMemberPhone(String memberPhone) {
        this.memberPhone = memberPhone;
    }

    public String getMemberOrganizationName() {
        return memberOrganizationName;
    }

    public void setMemberOrganizationName(String memberOrganizationName) {
        this.memberOrganizationName = memberOrganizationName;
    }
}
