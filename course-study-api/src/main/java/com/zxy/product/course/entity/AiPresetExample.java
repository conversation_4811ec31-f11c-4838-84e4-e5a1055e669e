package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AiPresetExampleEntity;

/**
 * 数智导师 模型预设实例POJO
 * <AUTHOR>
 * @date 2024年04月23日 15:29
 */
public class AiPresetExample extends AiPresetExampleEntity {
    private static final long serialVersionUID = -4986199973596665254L;
    public static final Integer GENERAL_QUESTION = 1;
    public static final Integer EXCLUSIVE_QUESTION = 0;

    /**组织：数据权限相关*/
    private Organization organization;

    /**常量：学员端预设问答随机值*/
    public static final Integer RandomPreset =3;

    /**资源类型 课程0 专题2*/
    private Integer recourseType;

    /**资源名称 课程名称 ||  专题名称*/
    private String resourceName;

    public Organization getOrganization() { return organization; }

    public void setOrganization(Organization organization) { this.organization = organization; }

    public Integer getRecourseType() { return recourseType; }

    public void setRecourseType(Integer recourseType) { this.recourseType = recourseType; }

    public String getResourceName() { return resourceName; }

    public void setResourceName(String resourceName) { this.resourceName = resourceName; }

    @Override
    public String toString() {
        return "AiPresetExample{" +
                "organization=" + organization +
                ", recourseType=" + recourseType +
                ", resourceName='" + resourceName + '\'' +
                '}';
    }
}
