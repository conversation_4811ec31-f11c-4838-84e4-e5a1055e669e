package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CertificateRecordEntity;

import java.util.ArrayList;
import java.util.List;

public class CertificateRecord extends CertificateRecordEntity {
    private static final long serialVersionUID = 1683409990130516646L;

    public static final int ISSUE_CERTIFICATE_TYPE_QUALIFIED = 1;// 合格
    public static final int ISSUE_CERTIFICATE_TYPE_EXCELLENT = 2;// 优秀
    public static final int BUSINESS_TYPE_COURSE = 0;// 课程
    public static final int BUSINESS_TYPE_SUBJECT = 1;// 专题
    public static final int BUSINESS_TYPE_SUB_AUTHENTICATED = 9;// 子认证

    public static final Integer TEMPLATE_MEMBER_NAME_COLUMN = 0;
    public static final Integer TEMPLATE_SUBJECT_CODE = 1;
    public static final Integer TEMPLATE_MEMBER_FULL_NAME_COLUMN = 2;

    // 获取方式，0自动 1手动发放
    public static final Integer ACCESS_TYPE_AUTO = 0;
    public static final Integer ACCESS_TYPE_IMPORT = 1;

    public static final String TEMPLATE_MEMBER_NAME_CN = "网大员工编号(必填)";
    public static final String TEMPLATE_SUBJECT_CODE_CN = "专题编码(必填)";
    public static final String TEMPLATE_MEMBER_FULL_NAME_CN = "网大员工姓名(必填)";



    private int row; // 行数，导入时辅助
    private String memberName;
    private String subjectCode;
    private String memberFullName;
    private List<RowError> errors = new ArrayList<>();

    private Member member;
    private CourseInfo courseInfo;
    private BusinessCertificate businessCertificate;

    public BusinessCertificate getBusinessCertificate() {
        return businessCertificate;
    }

    public void setBusinessCertificate(BusinessCertificate businessCertificate) {
        this.businessCertificate = businessCertificate;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }
}
