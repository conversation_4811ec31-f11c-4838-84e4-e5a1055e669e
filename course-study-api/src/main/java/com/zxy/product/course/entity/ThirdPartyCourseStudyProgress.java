package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.ThirdPartyCourseStudyProgressEntity;

public class ThirdPartyCourseStudyProgress extends ThirdPartyCourseStudyProgressEntity {

    private static final long serialVersionUID = -2780877111057162950L;
    private String  title;//课程标题
    private String  subTitle;//课题子标题
    private Integer type;//类型
    private String  cover;//封面
    private String  linkUrl;//课程访问路径
    private Integer source;//课程来源
    private Integer sumStudyTime;//统计学习时长

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getSumStudyTime() {
        return sumStudyTime;
    }

    public void setSumStudyTime(Integer sumStudyTime) {
        this.sumStudyTime = sumStudyTime;
    }
}
