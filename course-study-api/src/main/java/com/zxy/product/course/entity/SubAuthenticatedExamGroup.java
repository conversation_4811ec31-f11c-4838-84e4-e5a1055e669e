package com.zxy.product.course.entity;

import java.io.Serializable;

public class SubAuthenticatedExamGroup implements Serializable {

    private static final long serialVersionUID = -4403380477563321998L;

    private String  id;
    private Long    createTime;
    private String  subAuthenticatedId;
    private String  examGroupId;
    private String  businessId;
    private String  businessName;
    private Integer businessType;
    private Integer order;
    private Integer examTimes;
    private Integer examRegistrationFrequency;

    public void setExamRegistrationFrequency(Integer examRegistrationFrequency) {
        this.examRegistrationFrequency = examRegistrationFrequency;
    }

    public SubAuthenticatedExamGroup(String id, Long createTime, String subAuthenticatedId, String examGroupId, String businessId, String businessName, Integer businessType, Integer order, Integer examTimes, Integer examRegistrationFrequency) {
        this.id = id;
        this.createTime = createTime;
        this.subAuthenticatedId = subAuthenticatedId;
        this.examGroupId = examGroupId;
        this.businessId = businessId;
        this.businessName = businessName;
        this.businessType = businessType;
        this.order = order;
        this.examTimes = examTimes;
        this.examRegistrationFrequency = examRegistrationFrequency;
    }

    public SubAuthenticatedExamGroup() {}

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getExamTimes() {
        return examTimes;
    }

    public void setExamTimes(Integer examTimes) {
        this.examTimes = examTimes;
    }

    public String getExamGroupId() {
        return examGroupId;
    }

    public void setExamGroupId(String examGroupId) {
        this.examGroupId = examGroupId;
    }

    public String getSubAuthenticatedId() {
        return subAuthenticatedId;
    }

    public void setSubAuthenticatedId(String subAuthenticatedId) {
        this.subAuthenticatedId = subAuthenticatedId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getExamRegistrationFrequency() {
        return examRegistrationFrequency;
    }
}
