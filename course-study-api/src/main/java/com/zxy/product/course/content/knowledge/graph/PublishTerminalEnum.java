package com.zxy.product.course.content.knowledge.graph;

import java.util.HashMap;
import java.util.Map;

/**
 * 发布终端枚举
 *
 * <AUTHOR>
 * @date 2023年10月20日 10:23
 */
public enum PublishTerminalEnum {
    /**在APP与PC端均可发布*/
    All(0,"PC&APP"),
    /**在PC端发布*/
    PC(1,"PC"),
    /**在APP端发布*/
    APP(2,"APP"),
    ;
    private Integer code;
    private String value;

    PublishTerminalEnum(int code, String value) {
        this.code=code;
        this.value=value;
    }

    public static Map<Integer, String> publishTerminalMap = new HashMap<>(18);

    static {
        for (PublishTerminalEnum type : values()) {
            publishTerminalMap.put(type.getCode(), type.getValue());
        }
    }

    public Integer getCode() { return code; }

    public void setCode(Integer code) { this.code = code; }

    public String getValue() { return value; }

    public void setValue(String value) { this.value = value; }
}
