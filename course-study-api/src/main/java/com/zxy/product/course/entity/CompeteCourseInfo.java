package com.zxy.product.course.entity;

import java.util.List;

import com.zxy.product.course.jooq.tables.pojos.CompeteCourseInfoEntity;
/**
 * @ClassName:  CompeteCourseInfo   
 * @Description: 内训师-参赛作品表  
 * @author: gao<PERSON><PERSON> 
 * @date:   2018年7月10日 上午10:16:40
 */
public class CompeteCourseInfo extends CompeteCourseInfoEntity {

	private static final long serialVersionUID = -3427714265142128696L;
	// 章节
	private List<CompeteCourseChapterSection> competeCourseChapterSections;
	// 附件
	private List<CompeteCourseAttachment> competeCourseAttachments;
	// 参赛讲师单位名称
	private String lecturerCompanyName;
	// 是否已投票（针对currentUser）
	private Integer isVote;
	// 课程排行
	private int rank = 1;
	
	
	

	public List<CompeteCourseChapterSection> getCompeteCourseChapterSections() {
		return competeCourseChapterSections;
	}

	public void setCompeteCourseChapterSections(List<CompeteCourseChapterSection> competeCourseChapterSections) {
		this.competeCourseChapterSections = competeCourseChapterSections;
	}

	public List<CompeteCourseAttachment> getCompeteCourseAttachments() {
		return competeCourseAttachments;
	}

	public void setCompeteCourseAttachments(List<CompeteCourseAttachment> competeCourseAttachments) {
		this.competeCourseAttachments = competeCourseAttachments;
	}

	public String getLecturerCompanyName() {
		return lecturerCompanyName;
	}

	public void setLecturerCompanyName(String lecturerCompanyName) {
		this.lecturerCompanyName = lecturerCompanyName;
	}

	public Integer getIsVote() {
		return isVote;
	}

	public void setIsVote(Integer isVote) {
		this.isVote = isVote;
	}
	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}
	
	
	
}
