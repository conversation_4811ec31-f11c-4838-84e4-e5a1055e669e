package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressEntity;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.UpdatableRecord;
import org.jooq.impl.TableImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;

/**
 * Created by keeley on 16/11/2.
 */
public class CourseStudyProgress extends CourseStudyProgressEntity {

    private static final long serialVersionUID = 4066873644709235326L;

    public static final int FINISH_STATUS_MARKSUCCESS = 4; // 标记完成
    public static final int FINISH_STATUS_GIVEUP = 3; // 已放弃
    public static final int FINISH_STATUS_FINISH = 2; // 已完成
    public static final int FINISH_STATUS_STUDY = 1; // 学习中
    public static final int FINISH_STATUS_DEFAULT = 0; //未开始

    public static final int SOURCE_XXTS = 2;// 学习推送

    public static final int IS_REQUIRED_NO = 0; //选修
    public static final int IS_REQUIRED_YES = 1; //必修

    // 注册类型
    public static final int TYPE_SELF = 1; // 自主注册
    public static final int TYPE_PUSH = 2; // 学习推送
    public static final String URI = "course-study/course-study-progress";
    public static final String BUSINESS_TYPE_COURSE0 = "0"; //类型判断使用

    private Member member;
    private Member markMember;
    private Organization organization;
    private Integer source; // 最初来源;
    private CourseInfo courseInfo;
    private StudyPushInfo studyPushInfo;
    private Integer finishNum;

    public Integer getFinishNum() {
        return finishNum;
    }

    public void setFinishNum(Integer finishNum) {
        this.finishNum = finishNum;
    }

    private boolean success;

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    private List<CourseSectionStudyProgress> sectionProgress;

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {

        this.organization = organization;

    }

    public Integer getSource() {
        return source;
    }

    public Member getMarkMember() {
        return markMember;
    }

    public void setMarkMember(Member markMember) {
        this.markMember = markMember;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public List<CourseSectionStudyProgress> getSectionProgress() {
        return sectionProgress;
    }

    public void setSectionProgress(List<CourseSectionStudyProgress> sectionProgress) {
        this.sectionProgress = sectionProgress;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public StudyPushInfo getStudyPushInfo() {
        return studyPushInfo;
    }

    public void setStudyPushInfo(StudyPushInfo studyPushInfo) {
        this.studyPushInfo = studyPushInfo;
    }

    public boolean isFinish() {
        return this.getFinishStatus()!=null && (this.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_FINISH
                || this.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
    }


    public CourseStudyProgress fill(Record r) {
        this.setId(r.get(COURSE_STUDY_PROGRESS.ID));
        this.setMemberId(r.get(COURSE_STUDY_PROGRESS.MEMBER_ID));
        this.setCourseId(r.get(COURSE_STUDY_PROGRESS.COURSE_ID));
        this.setBeginTime(r.get(COURSE_STUDY_PROGRESS.BEGIN_TIME));
        this.setType(r.get(COURSE_STUDY_PROGRESS.TYPE));
        this.setIsRequired(r.get(COURSE_STUDY_PROGRESS.IS_REQUIRED));
        this.setFinishStatus(r.get(COURSE_STUDY_PROGRESS.FINISH_STATUS));
        this.setFinishTime(r.get(COURSE_STUDY_PROGRESS.FINISH_TIME));
        this.setStudyTotalTime(r.get(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME));
        this.setRegisterTime(r.get(COURSE_STUDY_PROGRESS.REGISTER_TIME));
        this.setLastAccessTime(r.get(COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME));
        this.setCourseVersionId(r.get(COURSE_STUDY_PROGRESS.COURSE_VERSION_ID));
        this.setMarkMemberId(r.get(COURSE_STUDY_PROGRESS.MARK_MEMBER_ID));
        this.setMarkTime(r.get(COURSE_STUDY_PROGRESS.MARK_TIME));
        this.setCompletedRate(r.get(COURSE_STUDY_PROGRESS.COMPLETED_RATE));
        this.setCurrentChapterId(r.get(COURSE_STUDY_PROGRESS.CURRENT_CHAPTER_ID));
        this.setCurrentSectionId(r.get(COURSE_STUDY_PROGRESS.CURRENT_SECTION_ID));
        this.setPushId(r.get(COURSE_STUDY_PROGRESS.PUSH_ID));
        this.setVisits(r.get(COURSE_STUDY_PROGRESS.VISITS));
        this.setLastModifyTime(r.get(COURSE_STUDY_PROGRESS.LAST_MODIFY_TIME));
        return this;
    }
    public static Map<Field<?>,Object> getUpdateMap(TableImpl<?> table, CourseStudyProgress progress){
        Map<Field<?>,Object> map = new HashMap<>();
        Field<?>[] fields = getFields(table);
        Object[] values = getValues(progress);
        for (int i = 0; i < fields.length; i++) {
            map.put(fields[i], values[i]);
        }
        return map;
    }
    public static Field<?>[] getFields(TableImpl<?> table){
        return new Field<?>[]{
                table.field("f_id", String.class),
                table.field("f_member_id", String.class),
                table.field("f_course_id", String.class),
                table.field("f_begin_time", Long.class),
                table.field("f_type", Integer.class),
                table.field("f_is_required", Integer.class),
                table.field("f_finish_status", Integer.class),
                table.field("f_finish_time", Long.class),
                table.field("f_study_total_time", Integer.class),
                table.field("f_register_time", Long.class),
                table.field("f_last_access_time", Long.class),
                table.field("f_create_time", Long.class),
                table.field("t_course_version_id", String.class),
                table.field("f_mark_member_id", String.class),
                table.field("f_mark_time", Long.class),
                table.field("f_completed_rate", Integer.class),
                table.field("f_current_chapter_id", String.class),
                table.field("f_current_section_id", String.class),
                table.field("f_push_id", String.class),
                table.field("f_visits", Integer.class),
                table.field("f_last_modify_time", Long.class),
                table.field("f_completion_times", Integer.class),
                table.field("f_latest_completion_time", Long.class)

        };
    }
    public static Object[] getValues(CourseStudyProgress progress) {
        return new Object[]{
               progress.getId(),
               progress.getMemberId(),
               progress.getCourseId(),
               progress.getBeginTime(),
               progress.getType(),
               progress.getIsRequired(),
               progress.getFinishStatus(),
               progress.getFinishTime(),
               progress.getStudyTotalTime(),
               progress.getRegisterTime(),
               progress.getLastAccessTime(),
               progress.getCreateTime(),
               progress.getCourseVersionId(),
               progress.getMarkMemberId(),
               progress.getMarkTime(),
               progress.getCompletedRate(),
               progress.getCurrentChapterId(),
               progress.getCurrentSectionId(),
               progress.getPushId(),
               progress.getVisits(),
               progress.getLastModifyTime(),
               progress.getCompletionTimes(),
               progress.getLatestCompletionTime()
        };
    }
    public static List<UpdatableRecord<?>> records(List<Object> objects, TableImpl<?> table, DSLContext dslContext) {
        return objects.stream().map(obj -> {
            UpdatableRecord<?> r = (UpdatableRecord<?>) dslContext.newRecord(table, obj);
            int size = r.size();
            for (int i = 0; i < size; i++) {
                if (r.getValue(i) == null && !r.field(i).getDataType().nullable()) {
                    r.changed(i, false);
                }
            }
            return r;
        }).collect(Collectors.toList());
    }
}

