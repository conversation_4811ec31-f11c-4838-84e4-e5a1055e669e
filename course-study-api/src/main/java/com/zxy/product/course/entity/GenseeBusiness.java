package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeBusinessEntity;

/**
 * Created by <PERSON> on 2017/2/24.
 */
public class GenseeBusiness extends GenseeBusinessEntity {

    /**
     *
     */
    private static final long serialVersionUID = 6619454031259873354L;

    private CourseInfo courseInfo;

    /** 类型（0：考试,1：调研,2:评估,3:课程） */
    public static final Integer TYPE_EXAM = 0;
    public static final Integer TYPE_RESEARCH = 1;
    public static final Integer TYPE_EVALUATE = 2;
    public static final Integer TYPE_COURSE = 3;

    /** 是否发布 1是 */
    public static final Integer IS_PUBLISH_YES = 1;
    /** 是否发布 0否 */
    public static final Integer IS_PUBLISH_NO = 0;

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }


}
