package com.zxy.product.course.content.model.mentor;

/**
 * 数智导师——课程扭转状态枚举
 * <AUTHOR>
 * @date 2025年02月24日 10:27
 */
public enum MentorCourseTypeEnum {

    /**无需生成——未发送接口给九天，一般是字幕和播报未生成 */
    NoNeedBuild(0),

    /**未生成——字幕或播报已发布，但九天还未将智能摘要和问答传送回来*/
    NotGenerated(1),

    /**待审核——九天智能摘要和问答已接收，但还未将摘要进行审核*/
    ToBeReviewed(2),

    /**未审核通过——九天智能摘要和问答已接收，审核结果为不通过*/
    NotApproved(3),

    /**已审核通过——九天智能摘要和问答已接收，审核结果为已通过*/
    Approved(4),

    /**开启数智导师——开启数智导师，并且学员端展示审核通过的智能摘要内容，以及展示该课程的推荐问题*/
    TurnOnModelMentor(5),

    /**开启无摘要数智导师——开启数智导师，并且学员端无该课程智能摘要，展示通用的推荐问题*/
    TurnOnNoAbstractModelMentor(6),;

    Integer code;
    MentorCourseTypeEnum(int code) { this.code=code; }
    public Integer getCode() { return code; }
}
