package com.zxy.product.course.entity;

import java.io.Serializable;

/**
 * @ClassName : CourseStudyCurrentYearStatistics
 * @Description : 当年学习数据
 */
public class CourseStudyCurrentYearStatistics implements Serializable {
    private long    number;                 //学习人数
    private long    totalLengthTime;        //学习总时长 h
    private double  avgLengthTime;          //人均时长

    public CourseStudyCurrentYearStatistics() {}

    public CourseStudyCurrentYearStatistics(long number, long totalLengthTime) {
        this.number = number;
        this.totalLengthTime = totalLengthTime;
    }

    public CourseStudyCurrentYearStatistics(long number, long totalLengthTime, double avgLengthTime) {
        this.number = number;
        this.totalLengthTime = totalLengthTime;
        this.avgLengthTime = avgLengthTime;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public long getTotalLengthTime() {
        return totalLengthTime;
    }

    public void setTotalLengthTime(long totalLengthTime) {
        this.totalLengthTime = totalLengthTime;
    }

    public double getAvgLengthTime() {
        return avgLengthTime;
    }

    public void setAvgLengthTime(double avgLengthTime) {
        this.avgLengthTime = avgLengthTime;
    }
}
