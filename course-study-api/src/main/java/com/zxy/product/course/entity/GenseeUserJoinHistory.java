package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeUserJoinHistoryEntity;

/**
 * Created by <PERSON> on 2017/3/2.
 */
public class GenseeUserJoinHistory extends GenseeUserJoinHistoryEntity {
    private static final long serialVersionUID = 6216187704334145564L;
    public static final int GENSEE_USER_JOIN_TYPE_PC = 0; //pc
    public static final int GENSEE_USER_JOIN_TYPE_APP = 1; //app
    public static final int GENSEE_USER_TYPE_INTERNAL = 0; //内部用户
    public static final int GENSEE_USER_TYPE_EXTERNAL = 1; //外部用户

    private Member member;
    private Organization organization;
    private String companyName;
    private Integer duration; // 访问时长

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }
}
