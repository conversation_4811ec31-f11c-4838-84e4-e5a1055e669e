package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.ThematicMemberEntity;

/**
 * 专题班用户
 * <AUTHOR>
 *
 */
public class ThematicMember extends ThematicMemberEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 777916386585888268L;

	/** 已报名 */
	public static Integer STATUS_YES = 1;
	/** 未报名 */
	public static Integer STATUS_NO = 0;

	//取值 t_exam_regist 中 f_pass_status 状态
	/** 0：已及格 */
	public static Integer EXAM_STATUS_PASS = 0;
	/** 1：未及格； */
	public static Integer EXAM_STATUS_FAIL = 1;
	/** 2：已完成； */
	public static Integer EXAM_STATUS_COMPLETE = 2;

	/** 课程未完成 */
	public static Integer COURSE_STATUS_NO = 0;
	/** 课程已完成 */
	public static Integer COURSE_STATUS_YES = 1;

	/** null,0：未完成； */
	public static Integer CERTIFICATE_STATUS_NO = 0;
	/** 1：待领取； */
	public static Integer CERTIFICATE_STATUS_WAIT = 1;
	/** 2：已领取 */
	public static Integer CERTIFICATE_STATUS_YES = 2;

}
