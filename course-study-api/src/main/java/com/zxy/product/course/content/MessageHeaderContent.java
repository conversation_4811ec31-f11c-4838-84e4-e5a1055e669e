package com.zxy.product.course.content;

/**
 * <AUTHOR>
 *
 */
public class MessageHeaderContent {
    public static final String ID  = "id";
    public static final String ORGANIZATION_ID  = "organizationId";
    public static final String NEW_ORGANIZATION_ID  = "nweOrganizationId";
    public static final String TYPE = "type";
    public static final String MEMBER_ID = "memberId";
    public static final String MEMBER_OLD_ID = "member_id";
    public static final String COURSE_ID = "courseId";
    public static final String IDS  = "ids";
    public static final String PARAMS  = "params";
    public static final String BUSINESS_TYPE  = "business_type";
    public static final String BUSINESS_ID  = "business_id";
    public static final String WEBCAST_ID  = "webcast_id";
    public static final String GENSEE_ID  = "gensee_id";
    public static final String NAME = "name";
    public static final String FLAG = "flag";
    public static final String COURSE_STATUS = "course_status";
    public static final String EXAM_ID = "examId";
    // 发布类型
    public static final String COURSE_PUBLISH_TYPE = "coursePublishType" ;
    // 积分
    public static final String INTEGRAL_BUSINESS_ID = "integral_business_id";
    public static final String INTEGRAL_COUNT ="integral_count";
    public static final String INTEGRAL_MEMBER_ID = "integral_member_id";
    public static final String INTEGRAL_RULE_KEY = "integral_rule_key";
    public static final String INTEGRAL_DESCRIPTION = "integral_description";
    public static final String INTEGRAL_SCORE = "integral_score";
    public static final String INTEGRAL_CREATE_CLIENT = "INTEGRAL_CREATE_CLIENT";
    public static final String ABILITY_ID = "abilityId";


    public static final String STUDYDATE = "studyDate";// 学习记录提交日期
    public static final String SECTIONPROGRESSLIST = "sectionProgressList";//章节进度
    public static final String IP = "course_study_ip";
    public static final String ACTIVITY_TYPE = "activity_type";
    public static final String AUDIT_ID = "audit_id";//审核id

    /**
     * 学习时长
     */
    public static final String STUDYTIME = "studyTime";

    /**
     * 学习设备
     */
    public static final String STUDYCLIENTTYPE = "studyClientType";

    public static final String FINISHSTIME = "finishTime";

    /**
     * 九天同步数据使用
     */
    public static final String STARTTIME = "startTime";
    public static final String ENDTIME = "endTime";
    public static final String VISITS = "visits";


    /**
     * 课程举报,审核后发送消息
     */
    public static final String MESSAGE_SENDER_ID = "senderId";
    public static final String RECEIVER_IDS = "receiverIds";
    public static final String TEMPLATE_CODE = "template_code";//消息模板
    public static final String CONTENT = "content";
    public static final String DISCUSS_CONTENT = "discuss_content";

    public static final String STUDY_PLAN_NUM ="studyPlanNum"; //学习计划数量
    public static final String STUDY_PLAN_NUM_OPERATION ="studyPlanNumOperation"; //学习计划数量操作

    public static final String RATE="rate";



    //智能播报
    public static final String  TEXT="text";//村文本
    public static final String  FILE_NAME="fileName";//文件名
    public static final String  COURSE_SECTION_ID="courseSectionId";//章节id
    public static final String  ATTACHMENT_ID="attachmentId";//附件id
    public static final String  ATTACHMENT_PATH="path";//附件id
    public static final String  DURATION="duration";//附件id

    //子认证
    public static final String QUERY_PARAS = "queryParas";//子认证学习管理页面异步导出条件


    public static final String CURRENT_NUMBER = "currentNumber";//当前条数
    public static final String TOTAL_NUMBER = "totalNumber";//总条数
    public static final String ACTIVITY_FLAG = "activityFlag";//是否参与活动

    //新积分key
    public static final String RULE_KEY = "ruleKey";
    public static final String POINT_SOURCE_ID = "sourceId";


    //2024反腐将笔记更新移动到别的队列中
    public static final String INTELLIGENT_NOTE_COURSE_ID="intelligentNoteCourseId";
    public static final String INTELLIGENT_NOTE_MEMBER_ID="intelligentNoteMemberId";
    public static final String INTELLIGENT_NOTE_VERSION_ID="intelligentNoteVersionId";

    //IHR推送专题与培训班数据 专题计划培训班id 专题id  操作类型
    public static final String TRAIN_PROJECT_ID = "trainProjectId";
    public static final String TOPIC_ID = "topicId";
    public static final String OPERATION_STATE = "operationState";

    /**数智导师FTP传输成功后需要同步的课程Id集合*/
    public static final String SYNCHRONOUS_COURSE_COLLECT="courseIds";

    /**课程在线限流相关MQ参数*/
    public static final String ONLINE_BUSINESS_ID="businessId";
    public static final String ONLINE_MEMBER_ID="memberId";


    /**
     * 系统话题
     */
    public enum SystemTopicBusinessType {
        COURSE(0, 2),
        SUBJECT(1, 3),
        KNOWLEDGE(2, 4),
        GENSEE(3, 5);

        private int studyTopic;
        private int systemTopic;
        SystemTopicBusinessType(int studyTopic, int systemTopic) {
            this.studyTopic = studyTopic;
            this.systemTopic = systemTopic;
        }

        public static int getSystemBusinessType(int studyTopic) {
            for (SystemTopicBusinessType type : SystemTopicBusinessType.values()){
                if(type.getStudyTopic() == studyTopic){
                    return type.getSystemTopic();
                }
            }
            return 2;
        }

        public int getStudyTopic() {
            return studyTopic;
        }

        public int getSystemTopic() {
            return systemTopic;
        }
    }
}
