package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CertificateRecordChbnEntity;

/**
 * <AUTHOR>
 */
public class CertificateRecordChbn extends CertificateRecordChbnEntity {
    private static final long serialVersionUID = -6807745352737973328L;

    public static final int CERTIFICATE_STATE_NOT_RECEIVED = 0;// 未获得
    public static final int CERTIFICATE_STATE_UNCLAIMED = 1; // 待领取
    public static final int CERTIFICATE_STATE_RECEIVERD = 2;// 已领取
    public static final String CERTIFICATE_ID_CHBN = "chbn";// 证书id
    public static final String CERTIFICATE_ID_ZHZT = "zhzt";// 证书id
    public static final String BUSINESS_ID_CHBN = "chbn"; // 业务id
    public static final String BUSINESS_ID_ZHZT = "zhzt"; // 业务id

    public static final String CERTIFICATE_PREFIX_CHBN = "CHBN";// chbn证书前缀
    public static final String CERTIFICATE_PREFIX_ZHZT = "ZHZT";// zhzt证书前缀

    public static final String CERTIFICATE_ISSUE_AGENCY = "中国移动网上人才发展中心"; // 颁证机构

}
