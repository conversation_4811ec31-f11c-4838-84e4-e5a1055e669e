package com.zxy.product.course.entity;

import java.io.Serializable;

public class CourseLivingResult implements Serializable {

    private static final long serialVersionUID = -1190473954977447188L;

    private String name;
    private Long beginTime;
    private Integer status;
    private String flag;
    private Integer studyTotalTime;
    private String courseId;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Integer getStudyTotalTime() {
        return studyTotalTime;
    }

    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
}
