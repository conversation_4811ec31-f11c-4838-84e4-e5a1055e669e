package com.zxy.product.course.entity;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2017/2/14.
 */
public class GenseeAPIAddress implements Serializable {
    private static final long serialVersionUID = -4323194382184117125L;
    private String genseeAddURL;
    private String genseeUpdateURL;
    private String genseeDeleteURL;
    private String genseeFinishURL;
    private String genseeHistoryURL;

    public String getGenseeAddURL() {
        return genseeAddURL;
    }

    public void setGenseeAddURL(String genseeAddURL) {
        this.genseeAddURL = genseeAddURL;
    }

    public String getGenseeUpdateURL() {
        return genseeUpdateURL;
    }

    public void setGenseeUpdateURL(String genseeUpdateURL) {
        this.genseeUpdateURL = genseeUpdateURL;
    }

    public String getGenseeDeleteURL() {
        return genseeDeleteURL;
    }

    public void setGenseeDeleteURL(String genseeDeleteURL) {
        this.genseeDeleteURL = genseeDeleteURL;
    }

    public String getGenseeFinishURL() {
        return genseeFinishURL;
    }

    public void setGenseeFinishURL(String genseeFinishURL) {
        this.genseeFinishURL = genseeFinishURL;
    }

    public String getGenseeHistoryURL() {
        return genseeHistoryURL;
    }

    public void setGenseeHistoryURL(String genseeHistoryURL) {
        this.genseeHistoryURL = genseeHistoryURL;
    }
}
