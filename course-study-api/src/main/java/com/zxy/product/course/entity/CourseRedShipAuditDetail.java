package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseRedShipAuditDetailEntity;

/**
 * Created by TJ on 2022/10/26.
 */
public class CourseRedShipAuditDetail extends CourseRedShipAuditDetailEntity {
    private static final long serialVersionUID = -194623193372473634L;

    /**
     * 红船审核时长阈值 暂定6h
     */
    public static final Long RED_AUDIT_THRESHOLD = 21600000L;

    /**
     * 课件
     */
    public static final int TYPE_COURSE= 0;
    /**
     * 字幕
     */
    public static final int TYPE_CAPTION = 1;

    /**
     * 红船审核状态：-1 (数据库中并无此状态，主要为了页面筛选'-'查询)
     */
    public static final Integer RED_AUDIT_STATUS_NEGATIVE_1 = -1;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_0 = 0;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_1 = 1;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_2 = 2;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_3 = 3;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_4 = 4;

    /**
     * 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败'
     */
    public static final Integer RED_AUDIT_STATUS_5 = 5;

    /**
     * 复核状态：0：待复核，1：采纳，2：未采纳
     */
    public static final Integer CHECK_AUDIT_STATUS_0 = 0;

    /**
     * 复核状态：0：待复核，1：采纳，2：未采纳
     */
    public static final Integer CHECK_AUDIT_STATUS_1 = 1;

    /**
     * 复核状态：0：待复核，1：采纳，2：未采纳
     */
    public static final Integer CHECK_AUDIT_STATUS_2 = 2;

    public static final String[] RED_SHIP_EXTENTION = {
            "MP3","mp3","MP4","mp4","xls","xlsx","doc","docx","ppt","pptx","pdf"
    };

    /**
     * 课程红船审核配置编码
     */
    public static final String COURSE_RED_SHIP_AUDIT_CODE = "course_red_ship_audit";

    /**
     * 课程红船审核开关-不需要审核
     */
    public static final Integer COURSE_RED_SHIP_AUDIT_STATUS_0 = 0;

    /**
     * 课程红船审核送审内容 1名称2封面3简介4讲师5标签6课件
     */
    public static final Integer COURSE_RED_SHIP_AUDIT_SECTION_6 = 6;
    /**
     * 课程红船审核送审内容 1名称2封面3简介4讲师5标签6课件,7字幕
     */
    public static final Integer COURSE_RED_SHIP_AUDIT_SECTION_7 = 7;

    /**
     * 发送红船审核类型 8:课程
     */
    public static final Integer RED_SHIP_AUDIT_SECTION_TYPE_COURSE = 8;



    private CourseInfo courseInfo;


    /**
     * o=非最新字幕,1=最新字幕
     */
    public static final Integer COURSE_RED_SHIP_AUDIT_SECTION_TYPE_CAPTION_YES = 1;
    public static final Integer COURSE_RED_SHIP_AUDIT_SECTION_TYPE_CAPTION_NO = 0;


    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }
}
