package com.zxy.product.course.entity;

import java.util.List;

import com.zxy.product.course.jooq.tables.pojos.ThematicChapterEntity;

/**
 * 专题班主题章节
 * <AUTHOR>
 *
 */
public class ThematicChapter extends ThematicChapterEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -4831802048310500163L;


	private List<ThematicChapterSection> thematicChapterSections;

	public List<ThematicChapterSection> getThematicChapterSections() {
		return thematicChapterSections;
	}

	public void setThematicChapterSections(List<ThematicChapterSection> thematicChapterSections) {
		this.thematicChapterSections = thematicChapterSections;
	}

}
