package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.StudyPushObjectEntity;

public class StudyPushObject extends StudyPushObjectEntity {

    private static final long serialVersionUID = -7492419705249477514L;

    public static final Integer BUSINESS_TYPE_COURSE = 1;
    public static final Integer BUSINESS_TYPE_SUBJECT = 2;
    public static final Integer BUSINESS_TYPE_EXAM = 3;

    public static final Integer IS_REQUIRED_NO = 1;
    public static final Integer IS_REQUIRED_YES = 2;

    private CourseRegister courseRegister;// 课程注册记录，辅助与个人推送记录查询
    private CourseStudyProgress courseStudyProgress;// 课程学习记录，辅助与个人推送记录查询

    public CourseRegister getCourseRegister() {
        return courseRegister;
    }
    public void setCourseRegister(CourseRegister courseRegister) {
        this.courseRegister = courseRegister;
    }
    public CourseStudyProgress getCourseStudyProgress() {
        return courseStudyProgress;
    }
    public void setCourseStudyProgress(CourseStudyProgress courseStudyProgress) {
        this.courseStudyProgress = courseStudyProgress;
    }


}
