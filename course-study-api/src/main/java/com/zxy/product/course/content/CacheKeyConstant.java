package com.zxy.product.course.content;

/**
 * Created by keeley on 2017/7/20.
 */
public class CacheKeyConstant {
    public static final String COURSE_STUDY = "course_study";
    public static final String COURSE_CHAPTER_SECTION = "course-chapter-section";
    public static final String COURSE_CHAPTER_SECTION_LOG = "course-chapter-section-log";
    public static final String COURSE_CHAPTER_SECTION_PROCESS = "course-chapter-section-process";
    public static final String COURSE_TOTAL_SECTION = "course-total-section";
    public static final String COURSE_CHAPTER = "course-chapter";
    public static final String THEMATIC_CHAPTER = "thematic-chapter";
    public static final String COURSE_STUDY_STATISTICS = "course-study-statistics";
    public static final String COURSE_SPLIT_TABLE_NAME = "course-split-table-name";
    public static final String COURSE_SECTION_PROGRESS_SPLIT_TABLE_NAME = "course-section-progress-split-table-name";
    public static final String COURSE_SECTION_PROGRESS_SPLIT_TABLE_NAME2 = "course-section-progress-split-table-name-2";
    public static final String COURSE_SPLIT_TABLE_CONFIG_LIST = "course-split-table-config-list";
    public static final String CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER = "course-member-day-study-update-message-trigger";
    public static final String CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER_TRUE = "1";
    public static final String COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE = "course-or-subject-full-search-select-rule";
    public static final String COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE_TRUE = "1"; // 默认全模糊匹配
    public static final String ALL_COURSE_INFO_MAP_BY_BUSINESS_TYPE = "all-course-info-map-by-business-type";
    //中国移动以及内部组织受众项Id的查询
    public static final String COURSE_OR_SUBJECT_AUDIENCE_ITEM_IDS = "course-or-subject-audience-item-ds";
    //相关课程或者相关专题的缓存key
    public static final String RELATED_COURSE_OR_SUBJECT_BY_TOPICIDS = "related-course-or-subject-by-topicids";
    public static final String GUESS_INTERESTED_COURSE_AND_SUBJECT = "guess-interested-course-and-subject";
    //分表key
    public static final String SHARDING_CONFIG = "sharding-config";
    public static final String COURSE_INFO_BY_REFERENCE_ID = "course-info-by-reference-id";
    public static final String COURSE_INFO_BY_SECTION_ID = "course-info-by-section-id";
    public static final String FIND_ORGANIZATION_PATH_BY_MEMBER_ID = "find-by-organization-path-by-member-id";
    public static final String THIRD_PARTT_INIT_DATA="third-party-init-data";//喜马拉雅初始化数据同步入库

    // 异步更新直播的参与人数以及浏览人数
    public static final String  GENSEE_VIEW_NUMBER_MAP = "gensee-view-number-map";
    public static final String  GENSEE_VIEW_NUMBER_PREFIX = "gensee-view-number-prefix";
    public static final String GENSEE_ATTEND_NUMBER_MAP = "gensee-attend-number-map";
    public static final String GENSEE_ATTEND_NUMBER_PREFIX = "gensee-attend-number-prefix";

    // 党校总屏总学习人数缓存
    public static final String PARTY_SUMMARY_STUDY_NUMBER = "party-summary-study-number";
    public static final String PARTY_SUMMARY_STUDY_DATA = "party-summary-study-data";
    public static final String PARTY_HOT_TOPIC_LIST = "party-hot-topic-list";
    public static final String TOPIC_COURSE_VISITS_MAP = "topic-course-visits-map";
    public static final String TOPIC_COURSE_VISITS_PREFIX = "topic-course-visits-prefix";

    // 人才发展手动推送接口
    public static final String PUSH_KEY="pccw-data";

    // 手动注册专题
    public static final String REGIST_KEY="hand-regist-subject-for-id";
    // 面授课程导入 前缀
    public static final String OFFLINE_CLASS_PREFIX="offline_class_prefix:";


    public static final String DISPLAY_MODULE              = "display";                    //大屏模块前缀
    public static final String PER_ORG_DAY30_STUDY_RECORDS = "per-org-day30-study-data";   //各组织30天学习数据
    public static final String TOTAL_DAY30_STUDY_RECORDS   = "total-day30-study-data"  ;   //整个集团30天学习数据
    public static final String DAILY_TOTAL_STUDY_TIME      = "daily-total-study-time"  ;   //每天学习时长
    public static final String BUILT_RESOURCE              = "built-resource"          ;   //在库资源
    public static final String STUDY_ACTIVITIES            = "study-activities"        ;   //学习活动
    public static final String LIVING_TOP3            = "living-top3"        ;              //近三十天观看人次最多前三直播
    public static final String RESOURCE_TOP3            = "resource-top3"        ;              //近三十天观看人次最多前三课程
    public static final String TOTAL_MONTH12_STUDY_TIME_DAY            = "total-month12-study-time-day"        ;              //近12个月内学习趋势
    public static final String TOTAL_WEEK5_STUDY_TIME_DAY            = "total-week5-study-time-day"        ;              //近5周学习趋势
    public static final String ACTIVITY_VIEW              = "activity-view"          ;   //活动情况
    public static final String RESOURCE_VIEW              = "resource-view"          ;   //资源情况
    public static final String TOTAL_CURRENT_YEAR_STUDY_TIME_DAY         = "total-cur-year-study-time-day"          ;   //当年学习情况
    public static final String TOTAL_PROVINCE_CURRENT_YEAR_STUDY_TIME_DAY         = "total-province-cur-year-study-time-day"          ;   //当年省公司学习情况
    public static final String LEARNING_TREND_CHART         = "learning-trend-chart"          ;   //当年省公司学习情况
    // 个人中心 学习画像 学习时长趋势
    public static final String MEMBER_STUDY_TENDENCY = "member-study-tendency";

    // 个人中心 学习画像 学习偏好
    public static final String MEMBER_LEARNING_PREFERENCES = "member-learning-preferences";

    public static final String STUDY_PLAN ="study-plan";
    public static final String CHALLENGE_GROUP ="study-plan-challenge-group";
    public static final String CHALLENGE_UNIT ="study-plan-challenge-unit";
    //智能笔记数量
    public static final String SECTION_NOTE_COUNT ="section-note-count";
    public static final String ZXY_LOG ="zxy-log";

}
