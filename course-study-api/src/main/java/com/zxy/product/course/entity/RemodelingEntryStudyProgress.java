package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.RemodelingEntryStudyProgressEntity;

public class RemodelingEntryStudyProgress extends RemodelingEntryStudyProgressEntity {
    private static final long serialVersionUID = -8159430006859333059L;

    public static final int STUDY_STATUS_SIGN = 1; // 已报名
    public static final int STUDY_STATUS_QUIT = 2; // 已放弃
    public static final int FINISH_STATUS_FINISHED = 2; //已完成
    public static final int CERTIFICATE_STATUS_ACQUISITION = 1;// 已获得证书
    public static final int CERTIFICATE_STATUS_UNFINISH = 0;// 未获得证书

    private RemodelingRoleDetail remodelingRoleDetail;

    private String certificateId;

    public RemodelingRoleDetail getRemodelingRoleDetail() {
        return remodelingRoleDetail;
    }

    public void setRemodelingRoleDetail(RemodelingRoleDetail remodelingRoleDetail) {
        this.remodelingRoleDetail = remodelingRoleDetail;
    }

    public String getCertificateId() {
        return certificateId;
    }

    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    public boolean isFinish() {
        return this.getFinishStatus()!=null && (this.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_FINISH
                || this.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
    }
}
