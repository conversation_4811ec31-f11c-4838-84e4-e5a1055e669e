package com.zxy.product.course.entity;


import com.zxy.product.course.jooq.tables.pojos.MiguUserAccessEntity;

public class MiGuUserAccess extends MiguUserAccessEntity {

    public static final String SOURCE_TYPE_PC = "PC";
    public static final String SOURCE_TYPE_APP = "MOBILE";
    public static final int SOURCE_TYPE_PC_ENUM = 1;
    public static final int SOURCE_TYPE_APP_ENUM = 2;

    /**
     * 内部用户
     */
    public static final int MEMBER_TYPE_INTERNAL = 1;

    /**
     * 游客
     */
    public static final int MEMBER_TYPE_EXTERNAL = 2;

    /**
     * 时间排序
     */
    public static final int ORDER_TYPE_TIME = 1;
    /**
     * 组织排序
     */
    public static final int ORDER_TYPE_ORG = 2;

    private String name;
    private String mechanismName;
    private String organizationName;
    private Integer memberType;

    private Integer orgOrder;

    private Long orgCreateTime;

    private String contentName;
    private Integer visit;


    public Long getOrgCreateTime() {
        return orgCreateTime;
    }

    public void setOrgCreateTime(Long orgCreateTime) {
        this.orgCreateTime = orgCreateTime;
    }

    public Integer getOrgOrder() {
        return orgOrder;
    }

    public void setOrgOrder(Integer orgOrder) {
        this.orgOrder = orgOrder;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    private Integer visits;

    public Integer getVisits() {
        return visits;
    }

    public void setVisits(Integer visits) {
        this.visits = visits;
    }

    public String getMechanismName() {
        return mechanismName;
    }

    public void setMechanismName(String mechanismName) {
        this.mechanismName = mechanismName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContentName() {
        return contentName;
    }

    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    public Integer getVisit() {
        return visit;
    }

    public void setVisit(Integer visit) {
        this.visit = visit;
    }
}
