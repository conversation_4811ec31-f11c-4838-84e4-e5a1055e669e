package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.KnowledgeRedShipAuditEntity;

public class KnowledgeRedShipAudit extends KnowledgeRedShipAuditEntity {

    /**
     * 红船审核状态,0=审核中,1=审核通过,2=审核拒绝,3=超时,4=待审核
     */
    public static final int RED_AUDIT_STATUS_0 = 0;
    public static final int RED_AUDIT_STATUS_1 = 1;
    public static final int RED_AUDIT_STATUS_2 = 2;
    public static final int RED_AUDIT_STATUS_3 = 3;
    public static final int RED_AUDIT_STATUS_4 = 4;


    /**
     * 复核状态,0=待复核,1=采纳,2=未采纳
     */
    public static final int CHECK_AUDIT_STATUS_0 = 0;
    public static final int CHECK_AUDIT_STATUS_1 = 1;
    public static final int CHECK_AUDIT_STATUS_2 = 2;


    //知识-红船审核类型
    public static final int MATERIAL_EXT_TYPE_KNOWLEDGE = 9;

    private KnowledgeInfo knowledgeInfo;
    private KnowledgeCategory knowledgeCategory;
    private Member member;

    public KnowledgeInfo getKnowledgeInfo() {
        return knowledgeInfo;
    }

    public void setKnowledgeInfo(KnowledgeInfo knowledgeInfo) {
        this.knowledgeInfo = knowledgeInfo;
    }

    public KnowledgeCategory getKnowledgeCategory() {
        return knowledgeCategory;
    }

    public void setKnowledgeCategory(KnowledgeCategory knowledgeCategory) {
        this.knowledgeCategory = knowledgeCategory;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }
}
