package com.zxy.product.course.entity;

import java.util.List;

import com.zxy.product.course.jooq.tables.pojos.CourseChapterEntity;

/**
 * Created by keeley on 16/10/14.
 */
public class CourseChapter extends CourseChapterEntity {

    private static final long serialVersionUID = 1243254534911817068L;
    private List<CourseChapterSection> courseChapterSections;// 章节

    private CourseChapterSection courseChapterSection;
    private CourseAbility courseAbility;
    private Ability ability;
    private Integer abilityCompleted;

    public Integer getAbilityCompleted() {
        return abilityCompleted;
    }

    public void setAbilityCompleted(Integer abilityCompleted) {
        this.abilityCompleted = abilityCompleted;
    }

    public Ability getAbility() {
        return ability;
    }

    public void setAbility(Ability ability) {
        this.ability = ability;
    }

    /**
     * 专题评分表ID
     * 每个主题所对应的
     */
    private String scoringId;

    private String scoringName;

    public CourseAbility getCourseAbility() {
        return courseAbility;
    }

    public void setCourseAbility(CourseAbility courseAbility) {
        this.courseAbility = courseAbility;
    }

    public List<CourseChapterSection> getCourseChapterSections() {
        return courseChapterSections;
    }

    public void setCourseChapterSections(List<CourseChapterSection> courseChapterSections) {
        this.courseChapterSections = courseChapterSections;
    }

    public String getScoringId() {
        return scoringId;
    }

    public CourseChapter setScoringId(String scoringId) {
        this.scoringId = scoringId;
        return this;
    }

    public String getScoringName() {
        return scoringName;
    }

    public CourseChapter setScoringName(String scoringName) {
        this.scoringName = scoringName;
        return this;
    }

    public CourseChapterSection getCourseChapterSection() {
        return courseChapterSection;
    }

    public void setCourseChapterSection(CourseChapterSection courseChapterSection) {
        this.courseChapterSection = courseChapterSection;
    }
}
