package com.zxy.product.course.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 资源数量统计
 * <AUTHOR> zhouyong
 */
public class ResourceCountStatistics implements Serializable {

    private static final long serialVersionUID = 5099960410113083734L;

    private int courseCount;                                //在库课程 0

    private int subjectCount;                               //在库专题 2

    private int knowledgeCount;                             //在库知识 1

    private List<CourseCategoryStatistics> courseCategories;      //课程分类

    public ResourceCountStatistics() {
        this.courseCategories = new ArrayList<>();
    }

    public int getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(int courseCount) {
        this.courseCount = courseCount;
    }

    public int getSubjectCount() {
        return subjectCount;
    }

    public void setSubjectCount(int subjectCount) {
        this.subjectCount = subjectCount;
    }

    public int getKnowledgeCount() {
        return knowledgeCount;
    }

    public void setKnowledgeCount(int knowledgeCount) {
        this.knowledgeCount = knowledgeCount;
    }

    public List<CourseCategoryStatistics> getCourseCategories() {
        return courseCategories;
    }

    public void setCourseCategories(List<CourseCategoryStatistics> courseCategories) {
        this.courseCategories = courseCategories;
    }

    public void setResourceCount(Map<Integer,Integer> resourceCount) {
        resourceCount.forEach((k,v) -> {
            switch (k) {
                case 0:
                    setCourseCount(v);
                    break;
                case 1:
                    setKnowledgeCount(v);
                    break;
                case 2:
                    setSubjectCount(v);
                    break;
                default:
                    break;
            }
        });
    }

    public void calculateRatio(List<CourseCategoryStatistics> statistics ) {
        int totalCount = statistics.stream().map(CourseCategoryStatistics::getCount).reduce(Integer::sum).orElseGet(() -> 0);
        if (totalCount > 0) {
            AtomicInteger sum = new AtomicInteger();
            for (int i = 0; i < statistics.size(); i++) {
                CourseCategoryStatistics statistic = statistics.get(i);
                Integer value = statistic.getCount();
                int percent = new BigDecimal(String.valueOf(value.doubleValue()/totalCount)).setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue();
                sum.addAndGet(percent);
                if (i!= statistics.size()-1){
                    statistic.setRatio(percent);
                }else {
                    statistic.setRatio(100 + percent- sum.get());
                }
            }
            this.courseCategories = statistics;
        }
    }

}
