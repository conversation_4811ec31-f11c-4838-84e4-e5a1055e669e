package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubjectDirectionEntity;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/6/1.
 */
public class SubjectDirection extends SubjectDirectionEntity {

    // 专题方向
    // 5G技能项 1
    private static final Integer DIRECTION_ONE = 1;
    // NFV技能项 2
    private static final Integer DIRECTION_TWO = 2;
    // SDN技能项 3
    private static final Integer DIRECTION_THREE = 3;
    // CDN技能项 4
    private static final Integer DIRECTION_FOUR = 4;
    // 软件开发技能项 5
    private static final Integer DIRECTION_FIVE = 5;
    // 大数据与人工智能技能项 6
    private static final Integer DIRECTION_SIX = 6;
    // 云计算技能项 7
    private static final Integer DIRECTION_SENVEN = 7;
    // 云服务技能项 8
    private static final Integer DIRECTION_EIGHT = 8;
    // IoT技能项 9
    private static final Integer DIRECTION_NINE = 9;
    // 安全技能项 10
    private static final Integer DIRECTION_TEN = 10;
    private static final long serialVersionUID = 1687782346389752668L;

    // 专题名称
    private String subjectName;
    // 角色配置表ID
    private String roleDetailId;

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public String getRoleDetailId() {
        return roleDetailId;
    }

    public void setRoleDetailId(String roleDetailId) {
        this.roleDetailId = roleDetailId;
    }
}
