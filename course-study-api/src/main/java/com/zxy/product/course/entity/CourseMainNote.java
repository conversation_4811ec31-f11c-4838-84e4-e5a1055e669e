package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseMainNoteEntity;

import java.util.List;

/**
 * 数智导师V2：课程笔记POJO
 * <AUTHOR>
 * @date 2024年10月12日 11:04
 */
public class CourseMainNote extends CourseMainNoteEntity {
    public static final Integer ENABLE = 1;
    private static final long serialVersionUID = 7021657379782881557L;
    public static final Integer HAS_RELEASE = 1;
    public static final Integer NO_RELEASE = 0;
    public static final Integer CANCEL_RELEASE = 2;

    /**常量：课程笔记当前版本*/
    public static final Integer CURRENT_VERSION=1;

    /**常量：非课程笔记当前版本*/
    public static final Integer NOT_CURRENT_VERSION=0;

    /**回显字段（课程名称）*/
    private String courseName;

    /**回显字段（组织名称）*/
    private String orgName;

    /**回显字段（课程发布状态）*/
    private Integer status;

    /**回显字段（提交用户姓名）*/
    private String submitName;

    //  回显字段 发布用户姓名
    private String releaseMemberName;

    /**回显字段（课程版本笔记集合）*/
    private List<CourseMainNoteVersion> mainNoteVersionCollect;

    /**回显字段（审核状态 0待审核 1审核通过 2审核拒绝）*/
    private Integer auditStatus;

    private String orgId;
    private Integer switchMentor;

    public static final int BUSINESS_TYPE_COURSE = 0; //业务类型为课程
    public static final Integer BUSINESS_TYPE_SUBJECT = 2; //业务类型为专题

    public String getCourseName() { return courseName; }

    public void setCourseName(String courseName) { this.courseName = courseName; }

    public String getOrgName() { return orgName; }

    public void setOrgName(String orgName) { this.orgName = orgName; }

    public Integer getStatus() { return status; }

    public void setStatus(Integer status) { this.status = status; }

    public String getSubmitName() { return submitName; }

    public void setSubmitName(String submitName) { this.submitName = submitName; }

    public List<CourseMainNoteVersion> getMainNoteVersionCollect() { return mainNoteVersionCollect; }

    public void setMainNoteVersionCollect(List<CourseMainNoteVersion> mainNoteVersionCollect) { this.mainNoteVersionCollect = mainNoteVersionCollect; }

    public Integer getAuditStatus() { return auditStatus; }

    public void setAuditStatus(Integer auditStatus) { this.auditStatus = auditStatus; }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgId() {
        return orgId;
    }

    public String getReleaseMemberName() {
        return releaseMemberName;
    }

    public void setReleaseMemberName(String releaseMemberName) {
        this.releaseMemberName = releaseMemberName;
    }

    public Integer getSwitchMentor() {
        return switchMentor;
    }

    public void setSwitchMentor(Integer switchMentor) {
        this.switchMentor = switchMentor;
    }

    @Override
    public String toString() {
        return "CourseMainNote{" +
                "courseName='" + courseName + '\'' +
                ", orgName='" + orgName + '\'' +
                ", status=" + status +
                ", submitName='" + submitName + '\'' +
                ", releaseMemberName='" + releaseMemberName + '\'' +
                ", mainNoteVersionCollect=" + mainNoteVersionCollect +
                ", auditStatus=" + auditStatus +
                ", orgId='" + orgId + '\'' +
                '}';
    }
}
