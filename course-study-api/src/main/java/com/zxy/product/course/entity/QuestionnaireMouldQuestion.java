package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.QuestionnaireMouldQuestionEntity;

import java.util.List;

/**
 * 满意度问卷模板-问题关联表
 *
 * <AUTHOR>
 * @date 2021/3/5/0005 15:15
 */
public class QuestionnaireMouldQuestion extends QuestionnaireMouldQuestionEntity {
    /**
     * 是否必填 0-非必填 1-必填
     */
    public static final Integer QUESTION_REQUIRE_NOT = 0;
    /**
     * 是否必填 0-非必填 1-必填
     */
    public static final Integer QUESTION_REQUIRE = 1;

    /**
     * 是否每个课程都循环复用 0-否 1-是
     */
    public static final Integer QUESTION_CIRCLE_FLAG_NO = 0;
    /**
     * 是否每个课程都循环复用 0-否 1-是
     */
    public static final Integer QUESTION_CIRCLE_FLAG_YES = 1;
    private static final long serialVersionUID = 9020151576515873102L;
    public static final Integer QUESTION_SORT_4 = 4;


    private QuestionnaireQuestion questionnaireQuestion;

    private OfflineQuestionnaireAnswer offlineQuestionnaireAnswer;

    private Statistics statistics;

    private List<OfflineQuestionnaireAnswer> offlineQuestionnaireAnswerList;

    private OfflineCourseQuestionnaire offlineCourseQuestionnaire;



    public QuestionnaireQuestion getQuestionnaireQuestion() {
        return questionnaireQuestion;
    }

    public void setQuestionnaireQuestion(QuestionnaireQuestion questionnaireQuestion) {
        this.questionnaireQuestion = questionnaireQuestion;
    }

    public OfflineQuestionnaireAnswer getOfflineQuestionnaireAnswer() {
        return offlineQuestionnaireAnswer;
    }

    public void setOfflineQuestionnaireAnswer(OfflineQuestionnaireAnswer offlineQuestionnaireAnswer) {
        this.offlineQuestionnaireAnswer = offlineQuestionnaireAnswer;
    }

    public Statistics getStatistics() {
        return statistics;
    }

    public void setStatistics(Statistics statistics) {
        this.statistics = statistics;
    }

    public List<OfflineQuestionnaireAnswer> getOfflineQuestionnaireAnswerList() {
        return offlineQuestionnaireAnswerList;
    }

    public void setOfflineQuestionnaireAnswerList(List<OfflineQuestionnaireAnswer> offlineQuestionnaireAnswerList) {
        this.offlineQuestionnaireAnswerList = offlineQuestionnaireAnswerList;
    }

    public OfflineCourseQuestionnaire getOfflineCourseQuestionnaire() {
        return offlineCourseQuestionnaire;
    }

    public void setOfflineCourseQuestionnaire(OfflineCourseQuestionnaire offlineCourseQuestionnaire) {
        this.offlineCourseQuestionnaire = offlineCourseQuestionnaire;
    }
}
