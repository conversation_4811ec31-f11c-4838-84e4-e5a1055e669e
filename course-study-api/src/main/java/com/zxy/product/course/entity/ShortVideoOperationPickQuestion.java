package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.ShortVideoOperationPickQuestionEntity;

public class ShortVideoOperationPickQuestion extends ShortVideoOperationPickQuestionEntity {
    private static final long serialVersionUID = 4187853127827900374L;
    public static final Integer STATUS_UN_SUBMIT = 1;
    public static final Integer STATUS_FAILED = 3;
    public static final Integer STATUS_PASSED = 4;



    public static Integer convertStatus(String statusString) {
        switch (statusString) {
            case "已通过":
                return STATUS_PASSED;
            case "未通过":
                return STATUS_FAILED;
            default:
                return STATUS_UN_SUBMIT;
        }
    }
}
