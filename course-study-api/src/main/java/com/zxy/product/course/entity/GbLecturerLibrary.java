package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbLecturerLibraryEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbLecturerLibrary extends GbLecturerLibraryEntity {
    private static final long serialVersionUID = -1043190585853037780L;

    private String path;
    private String attachmentName;
    List<GbCourseClassification> gbCourseClassificationList;
    List<GbCourseLibrary> gbCourseLibraryList;
    private String memberName;

    public static final String TEMPLATE_LECTURER_NAME_CN = "讲师姓名(必填)";
    public static final String TEMPLATE_CODE_CN = "讲师编码(必填)";
    public static final String TEMPLATE_UNIT_CN = "讲师单位(必填)";
    public static final String TEMPLATE_JOB_CN = "讲师职务(必填)";
    public static final String TEMPLATE_PERSONAL_PROFILE_CN = "个人简介(选填)";
    public static final String TEMPLATE_OBTAIN_HONORS_CN = "获得荣誉(选填)";

    private int row;
    private List<RowError> errors = new ArrayList<>();
    public static final Integer TEMPLATE_LECTURER_NAME_COLUMN = 0;
    public static final Integer TEMPLATE_CODE_COLUMN = 1;
    public static final Integer TEMPLATE_UNIT_COLUMN = 2;
    public static final Integer TEMPLATE_JOB_COLUMN = 3;
    public static final Integer TEMPLATE_PERSONAL_PROFILE_COLUMN = 4;
    public static final Integer TEMPLATE_OBTAIN_HONORS_COLUMN = 5;
    /**
     * 导入限制条数
     */
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public List<GbCourseClassification> getGbCourseClassificationList() {
        return gbCourseClassificationList;
    }

    public void setGbCourseClassificationList(List<GbCourseClassification> gbCourseClassificationList) {
        this.gbCourseClassificationList = gbCourseClassificationList;
    }

    public List<GbCourseLibrary> getGbCourseLibraryList() {
        return gbCourseLibraryList;
    }

    public void setGbCourseLibraryList(List<GbCourseLibrary> gbCourseLibraryList) {
        this.gbCourseLibraryList = gbCourseLibraryList;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }
}
