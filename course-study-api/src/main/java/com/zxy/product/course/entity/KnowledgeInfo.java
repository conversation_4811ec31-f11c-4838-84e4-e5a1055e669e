package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.KnowledgeInfoEntity;

import java.util.List;

/**
 * @Author: TJ
 * @Date: 2017/2/15
 * @ModifyUser: TJ
 * @ModifyDate: 2017/2/15
 * 知识库
 */
public class KnowledgeInfo extends KnowledgeInfoEntity{

    private static final long serialVersionUID = -7340359633365780346L;

    public static final int RELEASE_STATUS_PUBLISH_NOT = 0; //未发布
    public static final int RELEASE_STATUS_PUBLISH = 1; //已发布
    public static final int RELEASE_STATUS_RED_BOAT_PRE_RELEASE = 2; //红船审核通过后直接发布
    public static final int AUDIT_STATUS_WAIT = 0;// 待审核
    public static final int AUDIT_STATUS_OK = 1;// 审核通过
    public static final int AUDIT_STATUS_NO = 2;// 审核不通过
    public static final int AUDIT_STATUS_UNDER_REVIEW = 3;// 审核中(红船)
    public static final int IS_DEL_YES = 1;// 已删除
    public static final int IS_DEL_NO = 0;// 未删除
    public static final int CREATE_CLIENT_STUDENT = 0;//学员端
    public static final int CREATE_CLIENT_MANAGE = 1;//管理员端
    public static final int BUSINESS_TYPE_OWN = 0; //自建
    public static final int BUSINESS_TYPE_COURSE = 1; //课程
    public static final int BUSINESS_TYPE_SUBJECT = 2; //专题
    public static final int BUSINESS_TYPE_LIVE = 3; //直播
    public static final int BUSINESS_TYPE_CLASS = 4; //班级
    public static final int BUSINESS_TYPE_USER = 5; //用户
    // 是否加精 0未加精 1已加精
    public static final int ESSENCE_STATUS_NO = 0;
    public static final int ESSENCE_STATUS_YES = 1;
    // 置顶状态 0未置顶 1已置顶
    public static final int TOP_STATUS_NO = 0;
    public static final int TOP_STATUS_YES = 1;
    // 是否允许下载：1允许 2不允许
    public static final int DOWNLOAD_NOT_ALLOWED = 2;
    public static final int DOWNLOAD_ALLOWED = 1;
    // 是否公开：1非公开 2公开
    public static final int IS_NOT_PUBLIC = 1;
    public static final int IS_PUBLIC = 2;
    // 是否由专家工作室创建 0否 1是
    public static final int FROM_STUDIO_YES = 1;
    public static final int FROM_STUDIO_NO = 0;

    private Member uploadMember;
    private Member auditMember;
    private KnowledgeCategory knowledgeCategory;
    private List<BusinessTopic> businessTopics;
    private List<AudienceItem> audienceItems;
//    private Integer integralScore;//获得积分
    private String categoryName;//所属目录
    private Member createMember;

    private KnowledgeRedShipAudit knowledgeRedShipAudit;

    public KnowledgeRedShipAudit getKnowledgeRedShipAudit() {
        return knowledgeRedShipAudit;
    }

    public void setKnowledgeRedShipAudit(KnowledgeRedShipAudit knowledgeRedShipAudit) {
        this.knowledgeRedShipAudit = knowledgeRedShipAudit;
    }

    public Member getUploadMember() {
        return uploadMember;
    }

    public void setUploadMember(Member uploadMember) {
        this.uploadMember = uploadMember;
    }

    public Member getAuditMember() {
        return auditMember;
    }

    public void setAuditMember(Member auditMember) {
        this.auditMember = auditMember;
    }

    public List<BusinessTopic> getBusinessTopics() {
        return businessTopics;
    }

    public void setBusinessTopics(List<BusinessTopic> businessTopics) {
        this.businessTopics = businessTopics;
    }

    public List<AudienceItem> getAudienceItems() {
        return audienceItems;
    }

    public void setAudienceItems(List<AudienceItem> audienceItems) {
        this.audienceItems = audienceItems;
    }

//	public Integer getIntegralScore() {
//        return integralScore;
//    }
//
//    public void setIntegralScore(Integer integralScore) {
//        this.integralScore = integralScore;
//    }
    public KnowledgeCategory getKnowledgeCategory() {
        return knowledgeCategory;
    }

    public void setKnowledgeCategory(KnowledgeCategory knowledgeCategory) {
        this.knowledgeCategory = knowledgeCategory;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Member getCreateMember() {
        return createMember;
    }

    public void setCreateMember(Member createMember) {
        this.createMember = createMember;
    }

    public enum FileType {
        MP4("mp4", 0),
        MP3("mp3", 1),
        DOC("doc", 2),
        DOCX("docx", 2),
        PDF("pdf", 3),
        XLS("xls", 4),
        XLSX("xlsx", 4),
        PPT("ppt", 5),
        PPTX("pptx", 5),
        EPUB("epub", 6),
        TXT("txt", 7);

        private int type;
        private String extension;
        FileType(String extension, int type) {
            this.extension = extension;
            this.type = type;
        }

        public static int getType(String extention) {
            for (FileType fileType : FileType.values()){
                if(extention.equals(fileType.getExtension())){
                    return fileType.getType();
                }
            }
            return -1;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getExtension() {
            return extension;
        }

    }

}
