package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.MultidimensionalStudentScoreSheet_00Entity;
import org.jooq.Field;
import org.jooq.impl.TableImpl;

import java.util.HashMap;
import java.util.Map;
import java.util.zip.CRC32;

import static com.zxy.product.course.jooq.Tables.*;

public class MultidimensionalStudentScoreSheet extends MultidimensionalStudentScoreSheet_00Entity {
    private MultidimensionalScoring multidimensionalScoring;
    public static class MultidimensionalStudentScoreSheetTable {
        private static final Map<Long, TableImpl<?>> tableMap = new HashMap<Long, TableImpl<?>>() {{
            put(0L, MULT<PERSON>IMENSIONAL_STUDENT_SCORE_SHEET_00);
            put(1L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01);
            put(2L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02);
            put(3L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03);
            put(4L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04);
            put(5L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05);
            put(6L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06);
            put(7L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07);
            put(8L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08);
            put(9L, MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09);
        }};

        public final TableImpl<?> table;
        public final Field<Long> CREATE_TIME;
        public final Field<String> ID, COURSE_ID, SCORING_SUBJECT_ID, CONTENT, MEMBER_ID;
        public final Field<Integer> FIRST_LATITUDE_SCORE, SECOND_LATITUDE_SCORE, THIRD_LATITUDE_SCORE, FOURTH_LATITUDE_SCORE, FIFTH_LATITUDE_SCORE;

        private MultidimensionalStudentScoreSheetTable(TableImpl<?> table) {
            this.table = table;
            ID = this.table.field("f_id", String.class);
            COURSE_ID = this.table.field("f_course_id", String.class);
            SCORING_SUBJECT_ID = this.table.field("f_scoring_subject_id", String.class);
            FIRST_LATITUDE_SCORE = this.table.field("f_first_latitude_score", Integer.class);
            SECOND_LATITUDE_SCORE = this.table.field("f_second_latitude_score", Integer.class);
            THIRD_LATITUDE_SCORE = this.table.field("f_third_latitude_score", Integer.class);
            FOURTH_LATITUDE_SCORE = this.table.field("f_fourth_latitude_score", Integer.class);
            FIFTH_LATITUDE_SCORE = this.table.field("f_fifth_latitude_score", Integer.class);
            CONTENT = this.table.field("f_content", String.class);
            MEMBER_ID = this.table.field("f_member_id", String.class);
            CREATE_TIME = this.table.field("f_create_time", Long.class);
        }

        public static MultidimensionalStudentScoreSheetTable getTable(String subjectId) {
            CRC32 crc32 = new CRC32();
            crc32.update(subjectId.getBytes());
            long mod = crc32.getValue() % 10;
            TableImpl<?> table = tableMap.get(mod);
            return new MultidimensionalStudentScoreSheetTable(table);
        }
    }
    public MultidimensionalScoring getMultidimensionalScoring() {
        return multidimensionalScoring;
    }
    public MultidimensionalStudentScoreSheet setMultidimensionalScoring(MultidimensionalScoring multidimensionalScoring) {
        this.multidimensionalScoring = multidimensionalScoring;
        return this;
    }
}
