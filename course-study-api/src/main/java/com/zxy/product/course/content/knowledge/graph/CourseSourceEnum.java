package com.zxy.product.course.content.knowledge.graph;

import java.util.HashMap;
import java.util.Map;

/**
 * 课程Or专题来源枚举
 *
 * <AUTHOR>
 * @date 2023年10月20日 11:12
 */
public enum CourseSourceEnum {
    /**内部录制来源*/
    InternalRecording(0,"内部录制"),
    /**外部引进来源*/
    ExternalIntroduction(1,"外部引进"),
    /**共建共享来源*/
    CoConstructionAndSharing(2,"共建共享"),
    /**自主研发来源*/
    IndependentResearchDevelopment(3,"自主研发"),
    /**其他来源*/
    Other(4,"其他"),
    /**工作室来源*/
    Studio(5,"工作室"),
    ;

    private Integer code;
    private String value;


    CourseSourceEnum(int code, String value) {
        this.code=code;
        this.value=value;
    }
    public static Map<Integer, String> courseSourceMap = new HashMap<>(18);

    static {
        for (CourseSourceEnum type : values()) {
            courseSourceMap.put(type.getCode(), type.getValue());
        }
    }

    public Integer getCode() { return code; }

    public void setCode(Integer code) { this.code = code; }

    public String getValue() { return value; }

    public void setValue(String value) { this.value = value; }
}
