package com.zxy.product.course.entity;

import java.io.Serializable;

/**
 * 专题发布范围
 * <AUTHOR> zhouyong
 */
public class SubjectPublishedScope implements Serializable {

    private static final long serialVersionUID = 5547346949246387691L;

    private String businessId;
    private String joinId;
    private String joinType;
    private String path;
    private int    depth;

    public SubjectPublishedScope() {
    }

    public SubjectPublishedScope(String businessId, String joinId, String joinType, String path, int depth) {
        this.businessId = businessId;
        this.joinId = joinId;
        this.joinType = joinType;
        this.path = path;
        this.depth = depth;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getJoinId() {
        return joinId;
    }

    public void setJoinId(String joinId) {
        this.joinId = joinId;
    }

    public String getJoinType() {
        return joinType;
    }

    public void setJoinType(String joinType) {
        this.joinType = joinType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public int getDepth() {
        return depth;
    }

    public void setDepth(int depth) {
        this.depth = depth;
    }
}
