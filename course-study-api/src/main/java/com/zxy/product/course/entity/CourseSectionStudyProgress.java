package com.zxy.product.course.entity;

import java.sql.Timestamp;
import java.util.List;

import com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyProgressEntity;
import org.jooq.DSLContext;
import org.jooq.InsertValuesStep20;
import org.jooq.Record;
import org.jooq.UpdateConditionStep;
import org.jooq.impl.TableImpl;

/**
 * Created by keeley on 16/11/2.
 */
public class CourseSectionStudyProgress extends CourseSectionStudyProgressEntity {

    private static final long serialVersionUID = -4547161197654093454L;
    /**
     * 标记完成
     */
    public static final Integer FINISH_STATUS_MARKSUCCESS = 4;
    /**
     * 已完成
     */
    public static final Integer FINISH_STATUS_FINISH = 2;
    /**
     * 学习中
     */
    public static final int FINISH_STATUS_STUDY = 1;
    /**
     * 未开始
     */
    public static final int FINISH_STATUS_UNSTART = 0;
    /**
     * 审核未通过
     */
    public static final int FINISH_STATUS_NOT_THROUGH = 6;

    /**
     * 待审核
     */
    public static final int FINISH_STATUS_AUDIT = 5;

    /**
     * 待评卷
     */
    public static final int FINISH_STATUS_MARK = 7;

    public static final int AUDIT_PASS_SUCCESS = 1;
    public static final int AUDIT_PASS_FAILED = 2;
    public static final int COMPLETED_RATE_STUDYING = 50;
    public static final int COMPLETED_RATE_FINISHED = 100;

    private Organization organization;
    private Member member;
    private List<CourseSectionProgressAttachment> sectionAttachments;
    private CourseChapterSection courseChapterSection;

    private CourseInfo courseInfo;

    private String commitMemberName; //提交人名
    private String auditMemberName; //审核人名
    private String sectionName;//节点名
    private Integer sectionType;//节点类型
    private String courseName;//课程名称
    private Integer required;//选必修
    private Integer singleStudyTime;//单次更新章节进度表的学习时长
    //是否继续播放 0:暂停 1:继续
    private Integer play;
    private String resourceId;
    private String abilityName; //能力名称
    private Integer progressPercentage; //进度百分比

    public String getAbilityName() {
        return abilityName;
    }

    public void setAbilityName(String abilityName) {
        this.abilityName = abilityName;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getSingleStudyTime() {
		return singleStudyTime;
	}

	public void setSingleStudyTime(Integer singleStudyTime) {
		this.singleStudyTime = singleStudyTime;
	}

	public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public List<CourseSectionProgressAttachment> getSectionAttachments() {
        return sectionAttachments;
    }

    public void setSectionAttachments(List<CourseSectionProgressAttachment> sectionAttachments) {
        this.sectionAttachments = sectionAttachments;
    }

    public CourseChapterSection getCourseChapterSection() {
        return courseChapterSection;
    }

    public void setCourseChapterSection(CourseChapterSection courseChapterSection) {
        this.courseChapterSection = courseChapterSection;
    }

    public String getCommitMemberName() {
        return commitMemberName;
    }

    public void setCommitMemberName(String commitMemberName) {
        this.commitMemberName = commitMemberName;
    }

    public String getAuditMemberName() {
        return auditMemberName;
    }

    public void setAuditMemberName(String auditMemberName) {
        this.auditMemberName = auditMemberName;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public Integer getSectionType() {
        return sectionType;
    }

    public void setSectionType(Integer sectionType) {
        this.sectionType = sectionType;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public Integer getRequired() {
        return required;
    }

    public void setRequired(Integer required) {
        this.required = required;
    }

    public Integer getPlay() {
        return play;
    }

    public void setPlay(Integer play) {
        this.play = play;
    }

    public Integer getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Integer progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public CourseSectionStudyProgress fill(TableImpl<?> csspTable, Record r) {
        this.setId(r.get(csspTable.field("f_id"), String.class));
        this.setMemberId(r.get(csspTable.field("f_member_id"), String.class));
        this.setCourseId(r.get(csspTable.field("f_course_id"), String.class));
        this.setSectionId(r.get(csspTable.field("f_section_id"), String.class));
        this.setBeginTime(r.get(csspTable.field("f_begin_time"), Long.class));
        this.setFinishStatus(r.get(csspTable.field("f_finish_status"), Integer.class));
        this.setFinishTime(r.get(csspTable.field("f_finish_time"), Long.class));
        this.setCompletedRate(r.get(csspTable.field("f_completed_rate"), Integer.class));
        this.setStudyTotalTime(r.get(csspTable.field("f_study_total_time"),Integer.class));
        this.setLastAccessTime(r.get(csspTable.field("f_last_access_time"), Long.class));
        this.setExamStatus(r.get(csspTable.field("f_exam_status"), Integer.class));
        this.setLessonLocation(r.get(csspTable.field("f_lesson_location"), String.class));
        this.setCreateTime(r.get(csspTable.field("f_create_time"), Long.class));
        this.setCommitTime(r.get(csspTable.field("f_commit_time"), Long.class));
        this.setSubmitText(r.get(csspTable.field("f_submit_text"), String.class));
        this.setAuditMemberId(r.get(csspTable.field("f_audit_member_id"), String.class));
        this.setScore(r.get(csspTable.field("f_score"), Integer.class));
        this.setComments(r.get(csspTable.field("f_comments"), String.class));
        this.setAuditPass(r.get(csspTable.field("f_audit_pass"), Integer.class));
        this.setVisits(r.get(csspTable.field("f_visits"), Integer.class));
        return this;
    }

    public UpdateConditionStep<?> updateById(TableImpl<?> table, DSLContext dslContext) {
        return dslContext.update(table)
                .set(table.field("f_member_id", String.class), this.getMemberId())
                .set(table.field("f_course_id", String.class), this.getCourseId())
                .set(table.field("f_section_id", String.class), this.getSectionId())
                .set(table.field("f_begin_time", Long.class), this.getBeginTime())
                .set(table.field("f_finish_status", Integer.class), this.getFinishStatus())
                .set(table.field("f_finish_time", Long.class), this.getFinishTime())
                .set(table.field("f_completed_rate", Integer.class), this.getCompletedRate())
                .set(table.field("f_study_total_time", Integer.class), this.getStudyTotalTime())
                .set(table.field("f_last_access_time", Long.class), this.getLastAccessTime())
                .set(table.field("f_exam_status", Integer.class), this.getExamStatus())
                .set(table.field("f_lesson_location", String.class), this.getLessonLocation())
                .set(table.field("f_create_time", Long.class), this.getCreateTime())
                .set(table.field("f_commit_time", Long.class), this.getCommitTime())
                .set(table.field("f_submit_text", String.class), this.getSubmitText())
                .set(table.field("f_audit_member_id", String.class), this.getAuditMemberId())
                .set(table.field("f_score", Integer.class), this.getScore())
                .set(table.field("f_comments", String.class), this.getComments())
                .set(table.field("f_audit_pass", Integer.class), this.getAuditPass())
                .set(table.field("f_visits", Integer.class), this.getVisits())
                .set(table.field("f_modify_date", Timestamp.class), new Timestamp(System.currentTimeMillis()))
                .where(table.field("f_id", String.class).eq(this.getId()));
    }

    public InsertValuesStep20 insert(TableImpl<?> table, DSLContext dslContext) {
        return dslContext.insertInto(table,
                table.field("f_id", String.class),
                table.field("f_member_id", String.class),
                table.field("f_course_id", String.class),
                table.field("f_section_id", String.class),
                table.field("f_begin_time", Long.class),
                table.field("f_finish_status", Integer.class),
                table.field("f_finish_time", Long.class),
                table.field("f_completed_rate", Integer.class),
                table.field("f_study_total_time", Integer.class),
                table.field("f_last_access_time", Long.class),
                table.field("f_exam_status", Integer.class),
                table.field("f_lesson_location", String.class),
                table.field("f_create_time", Long.class),
                table.field("f_commit_time", Long.class),
                table.field("f_submit_text", String.class),
                table.field("f_audit_member_id", String.class),
                table.field("f_score", Integer.class),
                table.field("f_comments", String.class),
                table.field("f_audit_pass", Integer.class),
                table.field("f_visits", Integer.class)
        ).values(this.getId(),
                this.getMemberId(),
                this.getCourseId(),
                this.getSectionId(),
                this.getBeginTime(),
                this.getFinishStatus(),
                this.getFinishTime(),
                this.getCompletedRate(),
                this.getStudyTotalTime(),
                this.getLastAccessTime(),
                this.getExamStatus(),
                this.getLessonLocation(),
                this.getCreateTime(),
                this.getCommitTime(),
                this.getSubmitText(),
                this.getAuditMemberId(),
                this.getScore(),
                this.getComments(),
                this.getAuditPass(),
                this.getVisits()
        );
    }

    public static Integer batchInsert(TableImpl<?> table, DSLContext dslContext, List<CourseSectionStudyProgress> list) {
        InsertValuesStep20<?, String, String, String, String, Long, Integer, Long, Integer, Integer, Long, Integer, String, Long, Long, String, String, Integer, String, Integer, Integer> insertInto
                = dslContext.insertInto(table,
                table.field("f_id", String.class),
                table.field("f_member_id", String.class),
                table.field("f_course_id", String.class),
                table.field("f_section_id", String.class),
                table.field("f_begin_time", Long.class),
                table.field("f_finish_status", Integer.class),
                table.field("f_finish_time", Long.class),
                table.field("f_completed_rate", Integer.class),
                table.field("f_study_total_time", Integer.class),
                table.field("f_last_access_time", Long.class),
                table.field("f_exam_status", Integer.class),
                table.field("f_lesson_location", String.class),
                table.field("f_create_time", Long.class),
                table.field("f_commit_time", Long.class),
                table.field("f_submit_text", String.class),
                table.field("f_audit_member_id", String.class),
                table.field("f_score", Integer.class),
                table.field("f_comments", String.class),
                table.field("f_audit_pass", Integer.class),
                table.field("f_visits", Integer.class));
        for (CourseSectionStudyProgress courseSectionStudyProgress : list) {
            insertInto = insertInto.values(courseSectionStudyProgress.getId(),
                    courseSectionStudyProgress.getMemberId(),
                    courseSectionStudyProgress.getCourseId(),
                    courseSectionStudyProgress.getSectionId(),
                    courseSectionStudyProgress.getBeginTime(),
                    courseSectionStudyProgress.getFinishStatus(),
                    courseSectionStudyProgress.getFinishTime(),
                    courseSectionStudyProgress.getCompletedRate(),
                    courseSectionStudyProgress.getStudyTotalTime(),
                    courseSectionStudyProgress.getLastAccessTime(),
                    courseSectionStudyProgress.getExamStatus(),
                    courseSectionStudyProgress.getLessonLocation(),
                    courseSectionStudyProgress.getCreateTime(),
                    courseSectionStudyProgress.getCommitTime(),
                    courseSectionStudyProgress.getSubmitText(),
                    courseSectionStudyProgress.getAuditMemberId(),
                    courseSectionStudyProgress.getScore(),
                    courseSectionStudyProgress.getComments(),
                    courseSectionStudyProgress.getAuditPass(),
                    courseSectionStudyProgress.getVisits()
            );
        }
        return insertInto.execute();
    }
}
