package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedContentConfigureEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class SubAuthenticatedContentConfigure extends SubAuthenticatedContentConfigureEntity {
    private static final long serialVersionUID = -6230083454508749624L;


    /**
     * 1:在线学习组 2：考试组 3：认证维度 4：资料审核 5：举报审核 6：占位模块
     */
    public static final Integer CONTENT_TYPE_STUDY = 1;
    public static final Integer CONTENT_TYPE_EXAM = 2;
    public static final Integer CONTENT_TYPE_DIMENSION = 3;
    public static final Integer CONTENT_TYPE_RECOURSE = 4;
    public static final Integer CONTENT_TYPE_ACCUSE = 5;
    public static final Integer CONTENT_TYPE_BLANK = 6;
    public static final String CONTENT_NAME_DIMENSION = "认证维度";
    public static final String CONTENT_NAME_RECOURSE = "资料审核";
    public static final String CONTENT_NAME_ACCUSE = "举证材料";
    public static final String CONTENT_NAME_BLANK = "占位模块";
    private String subAuthenticatedName;
    private Integer totalGroupStatus;
    private List<SubAuthenticatedStudyOnline> subAuthenticatedStudyOnlines;
    private List<SubAuthenticatedDimension> subAuthenticatedDimensions;
    private List<SubAuthenticatedExamGroup> subAuthenticatedExamGroups;
    private String examCertificatedRecordId;//考试证书


    public String getSubAuthenticatedName() {
        return subAuthenticatedName;
    }

    public void setSubAuthenticatedName(String subAuthenticatedName) {
        this.subAuthenticatedName = subAuthenticatedName;
    }

    public List<SubAuthenticatedStudyOnline> getSubAuthenticatedStudyOnlines() {
        return subAuthenticatedStudyOnlines;
    }

    public void setSubAuthenticatedStudyOnlines(List<SubAuthenticatedStudyOnline> subAuthenticatedStudyOnlines) {
        this.subAuthenticatedStudyOnlines = subAuthenticatedStudyOnlines;
    }

    public List<SubAuthenticatedDimension> getSubAuthenticatedDimensions() {
        return subAuthenticatedDimensions;
    }

    public void setSubAuthenticatedDimensions(List<SubAuthenticatedDimension> subAuthenticatedDimensions) {
        this.subAuthenticatedDimensions = subAuthenticatedDimensions;
    }

    public Integer getTotalGroupStatus() {
        return totalGroupStatus;
    }

    public void setTotalGroupStatus(Integer totalGroupStatus) {
        this.totalGroupStatus = totalGroupStatus;
    }

    public List<SubAuthenticatedExamGroup> getSubAuthenticatedExamGroups() {
        return subAuthenticatedExamGroups;
    }

    public void setSubAuthenticatedExamGroups(List<SubAuthenticatedExamGroup> subAuthenticatedExamGroups) {
        this.subAuthenticatedExamGroups = subAuthenticatedExamGroups;
    }

    public String getExamCertificatedRecordId() {
        return examCertificatedRecordId;
    }

    public void setExamCertificatedRecordId(String examCertificatedRecordId) {
        this.examCertificatedRecordId = examCertificatedRecordId;
    }
}
