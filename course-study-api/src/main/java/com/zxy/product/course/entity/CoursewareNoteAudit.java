package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CoursewareNoteAuditEntity;

import java.util.List;

/**
 * 数智导师课件笔记审核POJO
 * <AUTHOR>
 * @date 2024年10月08日 14:20
 */
public class CoursewareNoteAudit extends CoursewareNoteAuditEntity {
    private static final long serialVersionUID = -1480712136560164551L;

    /**常量：待审核*/
    public static final Integer TO_BE_REVIEWED=0;

    /**常量：审核通过*/
    public static final Integer APPROVED=1;

    /**常量：审核拒绝*/
    public static final Integer REVIEW_REJECTION=2;

    /**回显字段：组织名称*/
    private String orgName;

    /**回显字段：课程名称*/
    private String courseName;

    /**回显字段：课件名称*/
    private String sectionName;

    /**回显字段：审核用户名称*/
    private String auditMemberName;

    /**回显字段：发布状态*/
    private Integer status;

    /**回显字段：课件笔记摘要*/
    private String summaryContent;

    /**回显字段：课件笔记提交时间*/
    private Long submitTime;

    /**回显字段：提交用户名称*/
    private String submitMemberName;

    /**回显字段：课件笔记知识点集合*/
    private List<CoursewareNoteVersion> noteVersionCollect;

    public String getOrgName() { return orgName; }

    public void setOrgName(String orgName) { this.orgName = orgName; }

    public String getCourseName() { return courseName; }

    public void setCourseName(String courseName) { this.courseName = courseName; }

    public String getSectionName() { return sectionName; }

    public void setSectionName(String sectionName) { this.sectionName = sectionName; }

    public String getAuditMemberName() { return auditMemberName; }

    public void setAuditMemberName(String auditMemberName) { this.auditMemberName = auditMemberName; }

    public Integer getStatus() { return status; }

    public void setStatus(Integer status) { this.status = status; }

    public String getSummaryContent() { return summaryContent; }

    public void setSummaryContent(String summaryContent) { this.summaryContent = summaryContent; }

    public Long getSubmitTime() { return submitTime; }

    public void setSubmitTime(Long submitTime) { this.submitTime = submitTime; }

    public String getSubmitMemberName() { return submitMemberName; }

    public void setSubmitMemberName(String submitMemberName) { this.submitMemberName = submitMemberName; }

    public List<CoursewareNoteVersion> getNoteVersionCollect() { return noteVersionCollect; }

    public void setNoteVersionCollect(List<CoursewareNoteVersion> noteVersionCollect) { this.noteVersionCollect = noteVersionCollect; }

    @Override
    public String toString() {
        return "CoursewareNoteAudit{" +
                "orgName='" + orgName + '\'' +
                ", courseName='" + courseName + '\'' +
                ", sectionName='" + sectionName + '\'' +
                ", auditMemberName='" + auditMemberName + '\'' +
                ", status=" + status +
                ", summaryContent='" + summaryContent + '\'' +
                ", submitTime=" + submitTime +
                ", noteVersionCollect=" + noteVersionCollect +
                '}';
    }
}
