package com.zxy.product.course.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> zhouyong
 * @ClassName : CourseStudyTime
 * @Description : 课程学习时长
 * @date : 2021-10-27 18:33
 */
public class CourseStudyTime implements Serializable,Comparable<CourseStudyTime> {

    private static final long serialVersionUID = -648576704289808654L;

    private String  time;                 //日期 m-d

    private double  totalLengthTime;      //总学习时长

    private long    appLengthTime;        //app学习时长

    private long    number;               //学习人数

    private double avgLengthTime;         //人均学习时长

    private Integer   day;
    private Integer   month;
    private Integer   year;

    private double studyNum;

    public CourseStudyTime() { }

    public CourseStudyTime(String time) {
        this.time = time;
    }

    public CourseStudyTime(String time,Integer day) {
        this.time = time;
        this.day = day;
    }

    public CourseStudyTime(String time, double totalLengthTime, long appLengthTime) {
        this.time = time;
        this.totalLengthTime = totalLengthTime;
        this.appLengthTime = appLengthTime;
    }

    public CourseStudyTime(String time, double totalLengthTime, long appLengthTime, long number) {
        this.time = time;
        this.totalLengthTime = totalLengthTime;
        this.appLengthTime = appLengthTime;
        this.number = number;
    }

    public CourseStudyTime(String time, Integer day, double totalLengthTime, long number) {
        this.time = time;
        this.day = day;
        this.totalLengthTime = totalLengthTime;
        this.number = number;
    }


    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public double getTotalLengthTime() {
        return totalLengthTime;
    }

    public void setTotalLengthTime(double totalLengthTime) {
        this.totalLengthTime = totalLengthTime;
    }

    public long getAppLengthTime() {
        return appLengthTime;
    }

    public void setAppLengthTime(long appLengthTime) {
        this.appLengthTime = appLengthTime;
    }

    public CourseStudyTime calculateTotalLengthTime(List<Double> totalTimes) {
        totalTimes.stream().reduce(Double::sum).ifPresent(v -> this.totalLengthTime = new BigDecimal(String.valueOf(v/3600.0)).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue());
        return this;
    }

    public CourseStudyTime calculateAppLengthTime(List<Long> appTimes) {
        appTimes.stream().reduce(Long::sum).ifPresent(v -> this.appLengthTime = new BigDecimal(String.valueOf(v/3600.0)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue()); // s -> h
        return this;
    }

    public CourseStudyTime calculateNumber(List<Long> studyNum) {
        studyNum.stream().reduce(Long::sum).ifPresent(v -> this.number = new BigDecimal(String.valueOf(v/3600.0)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
        return this;
    }

    public CourseStudyTime calculateTotalAvgLengthTime(Long number , Long totalStudyTime) {
        this.avgLengthTime = BigDecimal.valueOf((double)totalStudyTime / number).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        return this;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public double getAvgLengthTime() {
        return avgLengthTime;
    }

    public void setAvgLengthTime(double avgLengthTime) {
        this.avgLengthTime = avgLengthTime;
    }

    @Override
    public int compareTo(CourseStudyTime o) {
        return this.time.compareTo(o.time);
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public double getStudyNum() {
        return studyNum;
    }

    public void setStudyNum(double studyNum) {
        this.studyNum = studyNum;
    }

    @Override
    public String toString() {
        return "CourseStudyTime{" +
                "time='" + time + '\'' +
                ", totalLengthTime=" + totalLengthTime +
                ", appLengthTime=" + appLengthTime +
                ", number=" + number +
                ", avgLengthTime=" + avgLengthTime +
                '}';
    }
}
