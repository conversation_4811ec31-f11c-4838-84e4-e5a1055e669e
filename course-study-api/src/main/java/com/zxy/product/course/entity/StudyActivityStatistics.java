package com.zxy.product.course.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhouyong
 */
public class StudyActivityStatistics implements Serializable {

    private static final long serialVersionUID = 7991170692530116311L;

    private String  title;                          //学习活动标题
    private Integer totalStudyVisits;               //总体学习人次
    private Integer totalStudyCounts;               //总体学习人数
    private String  totalFinishedRatio;             //总体完成率
    private List<OrgStudyActivityStatistics> orgStatistics;  //各组织学习统计

    public StudyActivityStatistics() {
        this.totalStudyVisits = 0;
        this.totalStudyCounts = 0;
        this.totalFinishedRatio = "0%";
        this.orgStatistics = new ArrayList<>();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getTotalStudyVisits() {
        return totalStudyVisits;
    }

    public void setTotalStudyVisits(Integer totalStudyVisits) {
        this.totalStudyVisits = totalStudyVisits;
    }

    public Integer getTotalStudyCounts() {
        return totalStudyCounts;
    }

    public void setTotalStudyCounts(Integer totalStudyCounts) {
        this.totalStudyCounts = totalStudyCounts;
    }

    public String getTotalFinishedRatio() {
        return totalFinishedRatio;
    }

    public void setTotalFinishedRatio(String totalFinishedRatio) {
        this.totalFinishedRatio = totalFinishedRatio;
    }

    public void setTotalFinishedRatio(Integer finishedCount,Integer rangeCount) {
        if (null == rangeCount || rangeCount == 0) {
            this.totalFinishedRatio = "0%";
            return;
        }
        int percent = new BigDecimal(String.valueOf(finishedCount.doubleValue()/rangeCount)).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue();
        this.totalFinishedRatio = percent+"%";
    }

    public List<OrgStudyActivityStatistics> getOrgStatistics() {
        return orgStatistics;
    }

    public void setOrgStatistics(List<OrgStudyActivityStatistics> orgStatistics) {
        this.orgStatistics = orgStatistics;
    }
}

