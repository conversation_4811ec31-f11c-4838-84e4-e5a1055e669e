package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.tables.pojos.CourseRegisterEntity;

/**
 * Created by keeley on 16/11/7.
 */
public class CourseRegister extends CourseRegisterEntity {

    private static final long serialVersionUID = 8248187340059439392L;
    // 注册类型
    public static final int TYPE_SELF = 1; // 自主注册
    public static final int TYPE_PUSH = 2; // 推送

    public static final int IS_REQUIRED_FALSE = 0; // 选修
    public static final int IS_REQUIRED_TRUE = 1; // 必修

    public static final int IS_REGISTER_FALSE = 0; // 是否已经注册过-否
    public static final int IS_REGISTER_TRUE = 1; // 是否已经注册过-是

    private CourseStudyProgress courseStudyProgress;

    public CourseStudyProgress getCourseStudyProgress() {
        return courseStudyProgress;
    }

    public void setCourseStudyProgress(CourseStudyProgress courseStudyProgress) {
        this.courseStudyProgress = courseStudyProgress;
    }
}
