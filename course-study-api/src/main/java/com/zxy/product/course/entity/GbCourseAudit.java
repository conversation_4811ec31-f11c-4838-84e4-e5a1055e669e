package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbCourseAuditEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbCourseAudit extends GbCourseAuditEntity {
    private static final long serialVersionUID = 247227126456079811L;

    public final static int AUDIT_A = 0;
    public final static int AUDIT_P = 1;
    public final static int AUDIT_R = 2;

    Member member;
    Organization organization;
    GbCourseLibrary gbCourseLibrary;
    GbLecturerLibrary gbLecturerLibrary;
    GbCourseClassification gbCourseClassification;

    public GbCourseLibrary getGbCourseLibrary() {
        return gbCourseLibrary;
    }

    public void setGbCourseLibrary(GbCourseLibrary gbCourseLibrary) {
        this.gbCourseLibrary = gbCourseLibrary;
    }

    public GbLecturerLibrary getGbLecturerLibrary() {
        return gbLecturerLibrary;
    }

    public void setGbLecturerLibrary(GbLecturerLibrary gbLecturerLibrary) {
        this.gbLecturerLibrary = gbLecturerLibrary;
    }

    public GbCourseClassification getGbCourseClassification() {
        return gbCourseClassification;
    }

    public void setGbCourseClassification(GbCourseClassification gbCourseClassification) {
        this.gbCourseClassification = gbCourseClassification;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
}
