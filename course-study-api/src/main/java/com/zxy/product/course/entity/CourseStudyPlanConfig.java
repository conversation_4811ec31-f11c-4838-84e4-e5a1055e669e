package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseStudyPlanConfigEntity;

public class CourseStudyPlanConfig extends CourseStudyPlanConfigEntity {

    public static final String URI = "course-study/course-study-plan-config";
    //是否推送到学习计划
    public static final Integer PUSH_LEARNING_PLAN_DEFULT = 0;//默认
    public static final Integer PUSH_LEARNING_PLAN_TRUE = 1;//推送
    public static final Integer PUSH_LEARNING_PLAN_FALSE = 2;//撤销

    //分配方式 0:不包含以下范围  1:包含以下范围
    public static final Integer DISTRIBUTION_WAYS_TRUE = 1;
    public static final Integer DISTRIBUTION_WAYS_FALSE = 0;
    /**
     * 业务类型 1：课程 2：专题
     */
    public static final Integer BUSINESS_TYPE_COURSE =1;
    public static final Integer BUSINESS_TYPE_SUBJECT =2;

}
