package com.zxy.product.course.entity;


import java.io.Serializable;

/**
 * Created by <PERSON> on 2017/3/8.
 */
public class GenseeUserHistoryResult implements Serializable {
    private static final long serialVersionUID = -4375714244970234280L;
    private String nickname;// 昵称
    private Long joinTime;// 加入时间（详情见 3.3）
    private Long leaveTime;// 离开时间（详情见 3.5）
    private String ip;// IP 地址
    private String uid;// 用户 ID
    private String area;// 区域
    private String mobile;// 手机
    private String company;// 公司
    private String userdata;// 自定义参数。格式类似 URL parameter：
    // key1=value1&key2=value2… 需要获取数据后自行解析
	/*
	 * 值说明： 0 PC 客户端 1 PC Web 端 2 PC Web 端(http 流) 3 IPAD Web 端 4 IPHONE Web 端 5
	 * APAD Web 端 6 APHONE Web 端 7 IPAD APP 端 8 IPHONE APP 端 9 APAD APP 端 10
	 * APHONE APP 端 11 MAC 客户端 12 电话端 16 PLAYERSDK IOS 端 17 PLAYERSDK 安卓端
	 */
    private Long joinType;// 加入终端类型。

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Long getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(Long joinTime) {
        this.joinTime = joinTime;
    }

    public Long getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(Long leaveTime) {
        this.leaveTime = leaveTime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getUserdata() {
        return userdata;
    }

    public void setUserdata(String userdata) {
        this.userdata = userdata;
    }

    public Long getJoinType() {
        return joinType;
    }

    public void setJoinType(Long joinType) {
        this.joinType = joinType;
    }
}
