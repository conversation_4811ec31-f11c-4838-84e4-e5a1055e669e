package com.zxy.product.course.entity;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2017/2/14.
 */
public class GenseeLoginInfo implements Serializable {
    private static final long serialVersionUID = -2065961525469014386L;
    private  String portAddress;
    private  String loginName;
    private  String passWord;
    private  String site;

    public GenseeLoginInfo(String portAddress, String loginName, String passWord, String site) {
        this.portAddress = portAddress;
        this.loginName = loginName;
        this.passWord = passWord;
        this.site = site;
    }

    public String getPortAddress() {
        return portAddress;
    }

    public void setPortAddress(String portAddress) {
        this.portAddress = portAddress;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassWord() {
        return passWord;
    }

    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }
}
