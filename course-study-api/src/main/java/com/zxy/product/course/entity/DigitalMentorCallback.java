package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.DigitalMentorCallbackEntity;

public class DigitalMentorCallback extends DigitalMentorCallbackEntity {

    /**
     * 业务类型 0=课程
     */
    public static final Integer BUSINESS_TYPE_COURSE = 0;

    /**
     * 业务类型 2-专题
     */
    public static final Integer BUSINESS_TYPE_SUBJECT = 2;


    /**
     * 默认初始化次数
     */
    public static final Integer REQUESTS_NUMBER = 0;

    /**
     * 最大次数
     */
    public static final int FREQUENCY_MAX =3;

}
