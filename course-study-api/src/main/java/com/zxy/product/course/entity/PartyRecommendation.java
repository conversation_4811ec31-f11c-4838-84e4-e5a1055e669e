package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.PartyRecommendationEntity;

public class PartyRecommendation extends PartyRecommendationEntity {
    private static final long serialVersionUID = -6157884850217292265L;

    private String name;
    /**所属部门*/
    private Organization organization;
    private Integer publishClient;
    private Integer businessType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Integer getPublishClient() {
        return publishClient;
    }

    public void setPublishClient(Integer publishClient) {
        this.publishClient = publishClient;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
}
