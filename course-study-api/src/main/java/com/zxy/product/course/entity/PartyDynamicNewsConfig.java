package com.zxy.product.course.entity;



import com.zxy.product.course.jooq.tables.pojos.PartyDynamicNewsConfigEntity;

import java.io.Serializable;

/**
 * 党校动态新闻配置
 * <AUTHOR>
 * @create 2024/4/18 15:03
 */
public class PartyDynamicNewsConfig extends PartyDynamicNewsConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    //状态  1 - 未发布   2 - 已发布
    public static Integer PARTY_NEWS_STATE_UNPUBLISHED = 1;
    public static Integer PARTY_NEWS_STATE_PUBLISHED = 2;
}
