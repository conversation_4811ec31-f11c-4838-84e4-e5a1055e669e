package com.zxy.product.course.entity;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class SubAuthenticatedProgress implements Serializable {
    private static final long serialVersionUID = -6230083454508749773L;
    private String subAuthenticatedId;
    private String registerId;
    private String memberId;//员工id
    private String memberName;//员工账号
    private String memberFullName;//员工姓名
    private String organizationName;//组织名称
    private String organizationId;//组织id
    private Long registerDate;//注册时间
    private Integer studyTotalTime;//总学习时长
    private Integer studyStatus;//学习状态 0未开始 1学习中 2学习完成
    public static final Integer STATUS_0 = 0;
    public static final Integer STATUS_1 = 1;
    public static final Integer STATUS_2 = 2;
    public static final Integer STATUS_4 = 4;

    public String getSubAuthenticatedId() {
        return subAuthenticatedId;
    }

    public void setSubAuthenticatedId(String subAuthenticatedId) {
        this.subAuthenticatedId = subAuthenticatedId;
    }

    public String getRegisterId() {
        return registerId;
    }

    public void setRegisterId(String registerId) {
        this.registerId = registerId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Long getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Long registerDate) {
        this.registerDate = registerDate;
    }

    public Integer getStudyTotalTime() {
        return studyTotalTime;
    }

    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    public Integer getStudyStatus() {
        return studyStatus;
    }

    public void setStudyStatus(Integer studyStatus) {
        this.studyStatus = studyStatus;
    }
}
