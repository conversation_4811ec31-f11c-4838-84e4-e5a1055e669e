package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.IntelligentBroadcastEntity;

public class IntelligentBroadcast extends IntelligentBroadcastEntity {

    /**
     * 0请求正常,
     * 1=没有输入文本,
     * 2=输入的文件名有非法字符,
     * 3=输入的文本有非法字符,
     * 4,请求超时,
     * 5=没有文本信息,
     * 6=上传ocr失败,
     * 7=ocr转换失败,
     * 9=ocr调用失败,
     * 10=解析ocr文件失败,
     * 11=播报转换中,
     * 12=播报转换失败
     * 13 =文本超长
     * 14 =上传失败，请求未应答请联系管理员处理
     * 15 = 播报请求超时，点击重新发送请求
     */
    public final static Integer CAUSE_OF_FAILURE_ZERO = 0;
    public final static Integer CAUSE_OF_FAILURE_NOE = 1;
    public final static Integer CAUSE_OF_FAILURE_TWO = 2;
    public final static Integer CAUSE_OF_FAILURE_THREE = 3;
    public final static Integer CAUSE_OF_FAILURE_FOUR = 4;
    public final static Integer CAUSE_OF_FAILURE_FIVE = 5;
    public final static Integer CAUSE_OF_FAILURE_SIX = 6;
    public final static Integer CAUSE_OF_FAILURE_SEVEN = 7;

    public final static Integer CAUSE_OF_FAILURE_NINE = 9;
    public final static Integer CAUSE_OF_FAILURE_TEN = 10;
    public final static Integer CAUSE_OF_FAILURE_ELEVEN = 11;
    public final static Integer CAUSE_OF_FAILURE_TWELVE = 12;
    public final static Integer CAUSE_OF_FAILURE_THIRTEEN = 13;
    public final static Integer CAUSE_OF_FAILURE_NO_DIRECTORY = 14;
    public final static Integer CAUSE_OF_FAILURE_REPORT_REQUEST_FAILED = 15;

/*
    *//**
     * 0=转换中, 1=转换失败,2=转成成功
     *//*
    public final static Integer THE_TRANSFORMATION = 0;
    public final static Integer CONVERSION_FAILURE = 1;
    public final static Integer CONVERSION_SUCCESS = 2;*/

    /**
     * 1=启用(发布),0=禁用(取消发布),2=生成中,3=未发布,4=生成失败,5=ocr转化中,6=orc识别成功
     */
    public final static Integer DISABLE = 0;
    public final static Integer TO_ENABLE_THE =1;
    public final static Integer THE_TRANSFORMATION = 2;
    public final static Integer UNPUBLISHED = 3;
    public final static Integer CONVERSION_FAILURE = 4;
    public final static Integer CAUSE_OF_FAILURE_EIGHT = 5;
    public final static Integer CAUSE_OF_FAILURE_FOURTEEN = 6;




    private  String courseChapterSectionId;

    private String resourceId;

    private String extention;



    public String getExtention() {
        return extention;
    }

    public void setExtention(String extention) {
        this.extention = extention;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getCourseChapterSectionId() {
        return courseChapterSectionId;
    }

    public void setCourseChapterSectionId(String courseChapterSectionId) {
        this.courseChapterSectionId = courseChapterSectionId;
    }
}
