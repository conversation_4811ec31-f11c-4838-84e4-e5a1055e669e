package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogEntity;
import org.jooq.*;
import org.jooq.impl.TableImpl;

/**
 * @Author: TJ
 * @Date: 2016/11/21
 * @ModifyUser: TJ
 * @ModifyDate: 2016/11/21
 */
public class CourseSectionStudyLog extends CourseSectionStudyLogEntity {

    private static final long serialVersionUID = -9160131182195028070L;

    public static final int CLIENT_TYPE_PC = 0;
    public static final int CLIENT_TYPE_APP = 1;

    public static final int FINISH_STATUS_UNSTART = 0;
    public static final int FINISH_STATUS_STUDY = 1;
    public static final int FINISH_STATUS_FINISH = 2;
    public static final int FINISH_STATUS_GIVEUP = 3;
    public static final int FINISH_STATUS_MARKSUCCESS = 4;

    private Integer resourceTotalTime;

    public void setResourceTotalTime(Integer resourceTotalTime) {
        this.resourceTotalTime = resourceTotalTime;
    }

    public Integer getResourceTotalTime() {
        return resourceTotalTime;
    }

    public InsertValuesStep17 insert(TableImpl<?> table, DSLContext dslContext) {
        return dslContext.insertInto(table,
                table.field("f_id", String.class),
                table.field("f_member_id", String.class),
                table.field("f_course_id", String.class),
                table.field("f_section_id", String.class),
                table.field("f_section_scrom_id", String.class),
                table.field("f_client_type", Integer.class),
                table.field("f_completed_rate", Integer.class),
                table.field("f_finish_status", Integer.class),
                table.field("f_study_time", Integer.class),
                table.field("f_create_time", Long.class),
                table.field("f_commit_time", Long.class),
                table.field("f_submit_text", String.class),
                table.field("f_audit_member_id", String.class),
                table.field("f_score", Integer.class),
                table.field("f_comments", String.class),
                table.field("f_exam_status", Integer.class),
                table.field("f_lesson_location", String.class)
        ).values(this.getId(),
                this.getMemberId(),
                this.getCourseId(),
                this.getSectionId(),
                this.getSectionScromId(),
                this.getClientType(),
                this.getCompletedRate(),
                this.getFinishStatus(),
                this.getStudyTime(),
                this.getCreateTime(),
                this.getCommitTime(),
                this.getSubmitText(),
                this.getAuditMemberId(),
                this.getScore(),
                this.getComments(),
                this.getExamStatus(),
                this.getLessonLocation()
        );
    }

    public CourseSectionStudyLog fill(TableImpl<?> logTable, Record r) {
        this.setId(r.get(logTable.field("f_id"), String.class));
        this.setMemberId(r.get(logTable.field("f_member_id"), String.class));
        this.setCourseId(r.get(logTable.field("f_course_id"), String.class));
        this.setSectionId(r.get(logTable.field("f_section_id"), String.class));
        this.setSectionScromId(r.get(logTable.field("f_section_scrom_id"), String.class));
        this.setClientType(r.get(logTable.field("f_client_type"), Integer.class));
        this.setCompletedRate(r.get(logTable.field("f_completed_rate"), Integer.class));
        this.setFinishStatus(r.get(logTable.field("f_finish_status"), Integer.class));
        this.setStudyTime(r.get(logTable.field("f_study_time"), Integer.class));
        this.setCreateTime(r.get(logTable.field("f_create_time"), Long.class));
        this.setCommitTime(r.get(logTable.field("f_commit_time"), Long.class));
        this.setSubmitText(r.get(logTable.field("f_submit_text"), String.class));
        this.setAuditMemberId(r.get(logTable.field("f_audit_member_id"), String.class));
        this.setScore(r.get(logTable.field("f_score"), Integer.class));
        this.setComments(r.get(logTable.field("f_comments"), String.class));
        this.setExamStatus(r.get(logTable.field("f_exam_status"), Integer.class));
        this.setLessonLocation(r.get(logTable.field("f_lesson_location"), String.class));
        return this;
    }

}
