package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.RemodelingRoleDetailEntity;

public class RemodelingRoleDetail extends RemodelingRoleDetailEntity {
    private static final long serialVersionUID = -8111256627366097587L;

    public static final int SORT_HOME_PAGE = 1;// 重塑培训首页排序
    public static final int SORT_SING_PAGE = 2;// 重塑培训报名页排序

    /**
     * 是否为父级专题
     * 1.是， 0.否
     */
    public static final int IS_PARENT_YES = 1;
    public static final int IS_PARENT_NO = 0;



    public  enum ChapterAbbreviation{

        FIVE_G(1,"5G"),
        YUN_GAI(2,"云"),
        SAFE(3,"安全"),
        SOFTWARE(4,"软件开发"),
        NEW_FIVE_G(5,"5G"),
        NEW_YUN_GAI(6,"云");

        private final int type;
        private final String name;

        ChapterAbbreviation(int type,String name){
            this.type=type;
            this.name=name;
        }
        public static String getName(int type) {
            for (ChapterAbbreviation c : ChapterAbbreviation.values()) {
                if (c.getType() == type) {
                    return c.name;
                }
            }
            return null;
        }

        private int getType() {
            return type;
        }
    }

    public enum SectionAbbreviation{
        wireless_net(1,"5G无线网"),
        core_net(2,"5G核心网"),
        transmission_net(3,"5G传输网"),
        data_intelligence(4,"大数据和人工智能"),
        cloud_service(5,"云服务"),
        cloud_calculate(6,"云计算"),
        safe(7,"安全核心技术人才技能重塑"),
        software(8,"软件开发核心技术人才技能重塑");

        private final int type;
        private final String name;

        SectionAbbreviation(int type,String name){
            this.type=type;
            this.name=name;
        }

        public static String getName(int type) {
            for (SectionAbbreviation s : SectionAbbreviation.values()) {
                if (s.getType() == type) {
                    return s.name;
                }
            }
            return null;
        }

        private int getType() {
            return type;
        }

    }

    public enum RoleLevel{
        primary(1,"初级"),
        intermediate(2,"中级"),
        senior(3,"高级");

        private final int type;
        private final String name;

        RoleLevel(int type,String name){
            this.type=type;
            this.name=name;
        }
        public static String getName(int type) {
            for (RoleLevel r : RoleLevel.values()) {
                if (r.getType() == type) {
                    return r.name;
                }
            }
            return null;
        }

        private int getType() {
            return type;
        }

    }



}
