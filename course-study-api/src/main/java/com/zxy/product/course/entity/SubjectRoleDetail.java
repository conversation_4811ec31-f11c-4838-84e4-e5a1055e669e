package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubjectRoleDetailEntity;

import java.util.List;

/**
 * Created by FengKai on 2018/6/1.
 */
public class SubjectRoleDetail extends SubjectRoleDetailEntity {

    private static final long serialVersionUID = 8094296995224078872L;
    private Boolean isRegister;// 用于存放人员是否有注册此角色专题
    private String iconPath;// 角色icon
    private Integer courseCount; // 专题的课程数量
    private Integer requiredCount; // 专题的必修数量
    private Integer finishedCount; // 已完成的章节

    private CourseInfo subjectInfo;
    private List<SubjectProblem> problems;
    private List<SubjectCourse> courses;


    public Boolean getIsRegister() {
        return isRegister;
    }

    public void setIsRegister(Boolean isRegister) {
        this.isRegister = isRegister;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public Integer getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(Integer courseCount) {
        this.courseCount = courseCount;
    }

    public Integer getRequiredCount() {
        return requiredCount;
    }

    public void setRequiredCount(Integer requiredCount) {
        this.requiredCount = requiredCount;
    }

    public Integer getFinishedCount() {
        return finishedCount;
    }

    public void setFinishedCount(Integer finishedCount) {
        this.finishedCount = finishedCount;
    }

    public CourseInfo getSubjectInfo() {
        return subjectInfo;
    }

    public void setSubjectInfo(CourseInfo subjectInfo) {
        this.subjectInfo = subjectInfo;
    }

    public List<SubjectProblem> getProblems() {
        return problems;
    }

    public void setProblems(List<SubjectProblem> problems) {
        this.problems = problems;
    }

    public List<SubjectCourse> getCourses() {
        return courses;
    }

    public void setCourses(List<SubjectCourse> courses) {
        this.courses = courses;
    }
}
