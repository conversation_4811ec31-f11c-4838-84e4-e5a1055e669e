package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.PartyOrganizationEntity;

public class PartyOrganization extends PartyOrganizationEntity {
    private static final long serialVersionUID = 7451763671425812417L;

    public static final String ORG_TYPE_PARTY_COMMITTEE = "党委";
    public static final String ORG_TYPE_PARTY_BANCH = "党支部";
    public static final String ORG_TYPE_PARTY_GROUP = "党组";
    public static final String ORG_TYPE_PARTY_GENERAL_BANCH = "党总支";

    private Integer type;
    private String administrationOrgId;

    public Integer getType() {
      return type;
    }

    public void setType(Integer type) {
      this.type = type;
    }

    public String getAdministrationOrgId() {
      return administrationOrgId;
    }

    public void setAdministrationOrgId(String administrationOrgId) {
      this.administrationOrgId = administrationOrgId;
    }
}
