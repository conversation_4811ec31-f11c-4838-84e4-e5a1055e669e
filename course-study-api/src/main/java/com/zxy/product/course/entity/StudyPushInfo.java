package com.zxy.product.course.entity;

import java.util.List;

import com.zxy.product.course.jooq.tables.pojos.StudyPushInfoEntity;

public class StudyPushInfo extends StudyPushInfoEntity {

    private static final long serialVersionUID = -2761798502585441183L;
    /**1：草稿 */
	public static final Integer STATUS_DRAFT = 1;
	/**2：推送中*/
	public static final Integer STATUS_PROCESS = 2;
	/**3：暂停中*/
	public static final Integer STATUS_STOP = 3;
	/**4：已完成*/
	public static final Integer STATUS_COMPLETE = 4;

	//推送类型  1.自建,2.群主,3.职位,4.职务
	public static final int TYPE_SELF = 1;
	public static final int TYPE_TAG = 2;
	public static final int TYPE_POST = 3;
	public static final int TYPE_JOB = 4;


	public static final Integer IS_PUSH_NO = 0;// 是否正在推送-否
	public static final Integer IS_PUSH_YES = 1;// 是否正在推送-是

	// 所属部门
    private Organization organization;
    // 推送对象:课程,专题
    private List<StudyPushObject> pushObjects;
    // 受众项
    private List<AudienceItem> audienceItems;

    private Member member;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<StudyPushObject> getPushObjects() {
        return pushObjects;
    }

    public void setPushObjects(List<StudyPushObject> pushObjects) {
        this.pushObjects = pushObjects;
    }

    public List<AudienceItem> getAudienceItems() {
        return audienceItems;
    }

    public void setAudienceItems(List<AudienceItem> audienceItems) {
        this.audienceItems = audienceItems;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }



}
