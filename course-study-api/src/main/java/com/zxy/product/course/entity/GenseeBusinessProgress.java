package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeBusinessProgressEntity;

/**
 * Created by <PERSON> on 2017/2/24.
 */
public class GenseeBusinessProgress extends GenseeBusinessProgressEntity {

    private static final long serialVersionUID = 2022651872222863411L;


    /** 参与状态，0-未参与，1-已完成，2-待评卷，3-未及格 */
    public static final Integer FINISH_STATUS_NOT_JOIN = 0;
    public static final Integer FINISH_STATUS_FINISH = 1;
    public static final Integer FINISH_STATUS_WAIT_MARK = 2;
    public static final Integer FINISH_STATUS_NOT_PASS = 3;

    private GenseeBusiness genseeBusiness;

    public GenseeBusiness getGenseeBusiness() {
        return genseeBusiness;
    }

    public void setGenseeBusiness(GenseeBusiness genseeBusiness) {
        this.genseeBusiness = genseeBusiness;
    }




}
