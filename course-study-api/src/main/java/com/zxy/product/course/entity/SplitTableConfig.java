package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SplitTableConfigEntity;

public class SplitTableConfig extends SplitTableConfigEntity {

	private static final long serialVersionUID = 424417700742814672L;

	public static final Integer COURSE_SECTION_STUDY_LOG = 1;	// 课程log
	public static final Integer SUBJECT_SECTION_STUDY_LOG = 2;	// 专题log
	public static final Integer COURSE_STUDY_PROGRESS = 3;		// 学习进度
	public static final Integer COURSE_SECTION_STUDY_LOG_DAY = 4;	// 人-课-天
	public static final Integer SUBJECT_STUDY_LOG_DAY = 5;		// 人-专题-天
	
	private String orgName;	//组织名称
	private String orgShortName;	//组织简称

	private String path;	//组织层级ID

	public SplitTableConfig() {}

	public String getOrgShortName() {
		return orgShortName;
	}

	public void setOrgShortName(String orgShortName) {
		this.orgShortName = orgShortName;
	}

	public SplitTableConfig(String orgName, String organizationId, String targetTable) {
		this.orgName = orgName;
		this.setOrganizationId(organizationId);
		this.setTargetTable(targetTable);
	}

	public SplitTableConfig(String orgName,String path,String organizationId,String targetTable) {
		this.orgName = orgName;
		this.path = path;
		this.setOrganizationId(organizationId);
		this.setTargetTable(targetTable);
	}

	public SplitTableConfig(String orgName,String orgShortName,String path,String organizationId,String targetTable) {
		this.orgName = orgName;
		this.orgShortName=orgShortName;
		this.path = path;
		this.setOrganizationId(organizationId);
		this.setTargetTable(targetTable);
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	@Override
	public String toString() {
		return "SplitTableConfig{" +
			"organizationId='" + super.getOrganizationId() + '\'' +
			", orgName='" + orgName + '\'' +
			", targetTable='" + super.getTargetTable() + '\'' +
			", path='" + path + '\'' +
			'}';
	}
}
