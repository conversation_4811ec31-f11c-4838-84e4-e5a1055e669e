package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseCertificateRecordEntity;

/**
 * 用户领取证书记录
 * <AUTHOR>
 *
 */
public class CourseCertificateRecord extends CourseCertificateRecordEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -6433510727716223818L;

	//0：新动能通用课程证书
	/**
	 * 0：新动能通用课程证书
	 */
	public static final int TYPE_NEW_KINETIC_ENERGY = 0;

	//证书状态【0：未获得；1：待领取；2：已领取】
	/**
	 * 0：未获得
	 */
	public static final int STATE_NOT_OBTAINED = 0;
	/**
	 * 1：待领取
	 */
	public static final int STATE_WAIT_FOR_RECEIVED = 1;
	/**
	 * 2：已领取
	 */
	public static final int STATE_ALREADY_RECEIVED = 2;

	public static final int TOTAL_SUBJECT_NUM = 10; // 需要完成的专题总数量

}
