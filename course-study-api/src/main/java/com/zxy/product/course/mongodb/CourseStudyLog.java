package com.zxy.product.course.mongodb;

import org.springframework.data.annotation.Id;

import java.io.Serializable;

public class CourseStudyLog implements Serializable {

	private static final long serialVersionUID = 8257126109140912494L;
	@Id
	private String id;
	private String memberId;
	private String courseId;
	private String sectionId;
	private String referenceId;
	private String resourceId;
	private String SectionScromId;
	private Integer studyTime;
	private Integer clientType;
	private Long createTime;
	private Long commitTime;
	// 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成
	private Integer finishStatus;
	// 提交日期 yyyyMMdd
	private String commitDate;

	// 给批学习用
	private String lessonLocation;
	// 给批学习用
	private String comments;
	// 给批学习用
	private Integer resourceTotalTime;

	// 给分表时使用
	private Integer appStudyTime;
	private Integer pcStudyTime;
	private Integer year; // 年
	private Integer month;// 月
	private Integer day;// 日

	private Integer studyNum;


	public String getReferenceId() {
		return referenceId;
	}

	public void setReferenceId(String referenceId) {
		this.referenceId = referenceId;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getSectionId() {
		return sectionId;
	}

	public void setSectionId(String sectionId) {
		this.sectionId = sectionId;
	}

	public String getSectionScromId() {
		return SectionScromId;
	}

	public void setSectionScromId(String sectionScromId) {
		SectionScromId = sectionScromId;
	}

	public Integer getStudyTime() {
		return studyTime;
	}

	public void setStudyTime(Integer studyTime) {
		this.studyTime = studyTime;
	}

	public Integer getClientType() {
		return clientType;
	}

	public void setClientType(Integer clientType) {
		this.clientType = clientType;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getCommitTime() {
		return commitTime;
	}

	public void setCommitTime(Long commitTime) {
		this.commitTime = commitTime;
	}

	public Integer getFinishStatus() {
		return finishStatus;
	}

	public void setFinishStatus(Integer finishStatus) {
		this.finishStatus = finishStatus;
	}

	public String getCommitDate() {
		return commitDate;
	}

	public void setCommitDate(String commitDate) {
		this.commitDate = commitDate;
	}

	public String getLessonLocation() {
		return lessonLocation;
	}

	public void setLessonLocation(String lessonLocation) {
		this.lessonLocation = lessonLocation;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getResourceTotalTime() {
		return resourceTotalTime;
	}

	public void setResourceTotalTime(Integer resourceTotalTime) {
		this.resourceTotalTime = resourceTotalTime;
	}

	public Integer getAppStudyTime() {
		return appStudyTime;
	}

	public void setAppStudyTime(Integer appStudyTime) {
		this.appStudyTime = appStudyTime;
	}

	public Integer getPcStudyTime() {
		return pcStudyTime;
	}

	public void setPcStudyTime(Integer pcStudyTime) {
		this.pcStudyTime = pcStudyTime;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	public Integer getStudyNum() {
		return studyNum;
	}

	public void setStudyNum(Integer studyNum) {
		this.studyNum = studyNum;
	}

	public void forInsert(){
		this.setId(java.util.UUID.randomUUID().toString());
		this.setCreateTime(System.currentTimeMillis());
	}

}
