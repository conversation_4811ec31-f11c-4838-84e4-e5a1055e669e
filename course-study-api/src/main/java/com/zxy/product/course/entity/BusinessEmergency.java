package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.BusinessEmergencyEntity;

public class BusinessEmergency extends BusinessEmergencyEntity {

    /**
     * 类型:0=课程,1=专题,2=热门内容,3=发现页热议,4=推荐&猜你喜欢
     */
    public final static int BusinessTypeCourse = 0;
    /**
     * 类型:0=课程,1=专题,2=热门内容,3=发现页热议,4=推荐&猜你喜欢
     */
    public final static int BehaviorTypeSubject = 1;

    /**
     * 类型:0=课程,1=专题,2=热门内容,3=发现页热议,4=推荐&猜你喜欢
     */
    public final static int BehaviorTypeHotContent = 2;

    /**
     * 类型:0=课程,1=专题,2=热门内容,3=发现页热议,4=推荐&猜你喜欢
     */
    public final static int BehaviorTypeDiscoveryPageIsHotTopic = 3;

    /**
     * 类型:0=课程,1=专题,2=热门内容,3=发现页热议,4=推荐&猜你喜欢
     */
    public final static int BehaviorTypeCourseAndSubjectList = 4;


}
