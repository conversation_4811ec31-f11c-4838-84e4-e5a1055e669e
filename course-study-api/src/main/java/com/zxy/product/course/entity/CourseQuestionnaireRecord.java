package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseQuestionnaireRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CourseQuestionnaireRecord extends CourseQuestionnaireRecordEntity {
    private static final long serialVersionUID = -1769234751416113521L;

    /**
     * 答题状态：未提交
     */
    public static final Integer STATUS_0 = 0;
    /**
     * 答题状态：已提交
     */
    public static final Integer STATUS_1 = 1;

    /**
     * 类型 1：线上 2：线下
     */
    public static final Integer TYPE_ONLINE = 1;
    /**
     * 类型 1：线上 2：线下
     */
    public static final Integer TYPE_OFFLINE = 2;

    private Member member;

    private Organization organization;

    private QuestionnaireMould questionnaireMould;

    private List<OfflineCourseQuestionnaireChapter> offlineCourseQuestionnaireChapterList;

    private List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList;



    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public QuestionnaireMould getQuestionnaireMould() {
        return questionnaireMould;
    }

    public void setQuestionnaireMould(QuestionnaireMould questionnaireMould) {
        this.questionnaireMould = questionnaireMould;
    }

    public List<OfflineCourseQuestionnaireChapter> getOfflineCourseQuestionnaireChapterList() {
        return offlineCourseQuestionnaireChapterList;
    }

    public void setOfflineCourseQuestionnaireChapterList(List<OfflineCourseQuestionnaireChapter> offlineCourseQuestionnaireChapterList) {
        this.offlineCourseQuestionnaireChapterList = offlineCourseQuestionnaireChapterList;
    }

    public List<QuestionnaireMouldQuestion> getQuestionnaireMouldQuestionList() {
        return questionnaireMouldQuestionList;
    }

    public void setQuestionnaireMouldQuestionList(List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList) {
        this.questionnaireMouldQuestionList = questionnaireMouldQuestionList;
    }
}
