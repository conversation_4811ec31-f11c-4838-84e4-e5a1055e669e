package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.StudyPushRecordEntity;

public class StudyPushRecord extends StudyPushRecordEntity {

    private static final long serialVersionUID = -2339445645334117261L;

    public static Integer PUSH_STATUS_NOT_START = 1;
    public static Integer PUSH_STATUS_SUCCESS = 2;
    public static Integer PUSH_STATUS_FAIL = 3;
    public static Integer PUSH_STATUS_STOP = 4;// 已停止

    public static Integer TASK_STATUS_NOT_START = 1;
    public static Integer TASK_STATUS_PROGRESS = 2;
    public static Integer TASK_STATUS_SUCCESS = 3;

    // 推送人
    private Member pushMember;

    //推送对象
    private StudyPushObject pushObject;

    public Member getPushMember() {
        return pushMember;
    }

    public void setPushMember(Member pushMember) {
        this.pushMember = pushMember;
    }

    public StudyPushObject getPushObject() {
        return pushObject;
    }

    public void setPushObject(StudyPushObject pushObject) {
        this.pushObject = pushObject;
    }

}
