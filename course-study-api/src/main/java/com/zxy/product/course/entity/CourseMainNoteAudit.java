package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseMainNoteAuditEntity;

import java.util.List;

/**
 * 数智导师V2：课程笔记审核POJO
 * <AUTHOR>
 * @date 2024年10月12日 11:06
 */
public class CourseMainNoteAudit extends CourseMainNoteAuditEntity {
    private static final long serialVersionUID = 611258061262411371L;

    /**常量：待审核*/
    public static final Integer TO_BE_REVIEWED=0;

    /**常量：审核通过*/
    public static final Integer APPROVED=1;

    /**常量：审核拒绝*/
    public static final Integer REVIEW_REJECTION=2;

    /**回显字段：组织名称*/
    private String orgName;

    /**回显字段：课程名称*/
    private String courseName;

    /**回显字段：审核用户名称*/
    private String auditMemberName;

    /**回显字段：发布状态*/
    private Integer status;

    /**回显字段：课件笔记摘要*/
    private String summaryContent;

    /**回显字段：课件笔记提交时间*/
    private Long submitTime;

    /**回显字段：审核用户名称*/
    private String submitMemberName;

    /**回显字段：课程笔记知识点集合*/
    private List<CourseMainNoteVersion> mainNoteCollect;

    public String getOrgName() { return orgName; }

    public void setOrgName(String orgName) { this.orgName = orgName; }

    public String getCourseName() { return courseName; }

    public void setCourseName(String courseName) { this.courseName = courseName; }

    public String getAuditMemberName() { return auditMemberName; }

    public void setAuditMemberName(String auditMemberName) { this.auditMemberName = auditMemberName; }

    public Integer getStatus() { return status; }

    public void setStatus(Integer status) { this.status = status; }

    public String getSummaryContent() { return summaryContent; }

    public void setSummaryContent(String summaryContent) { this.summaryContent = summaryContent; }

    public Long getSubmitTime() { return submitTime; }

    public void setSubmitTime(Long submitTime) { this.submitTime = submitTime; }

    public String getSubmitMemberName() { return submitMemberName; }

    public void setSubmitMemberName(String submitMemberName) { this.submitMemberName = submitMemberName; }

    public List<CourseMainNoteVersion> getMainNoteCollect() { return mainNoteCollect; }

    public void setMainNoteCollect(List<CourseMainNoteVersion> mainNoteCollect) { this.mainNoteCollect = mainNoteCollect; }

    @Override
    public String toString() {
        return "CourseMainNoteAudit{" +
                "orgName='" + orgName + '\'' +
                ", courseName='" + courseName + '\'' +
                ", auditMemberName='" + auditMemberName + '\'' +
                ", status=" + status +
                ", summaryContent='" + summaryContent + '\'' +
                ", submitTime=" + submitTime +
                ", mainNoteCollect=" + mainNoteCollect +
                '}';
    }
}
