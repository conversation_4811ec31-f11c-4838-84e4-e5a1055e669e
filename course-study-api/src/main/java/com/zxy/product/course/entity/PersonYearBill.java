package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.PersonYearBillEntity;

public class PersonYearBill extends PersonYearBillEntity {

	private static final long serialVersionUID = 6290428170029605217L;

	public static final String CACHE_KEY = "person-year-bill";

	private String favoriteSubject;					// 学习时长最长的专题
	private Integer favoriteSubjectMemberCount;		// 该专题的学习总人数
	private Integer groupMemberNum;				// 集团总人数
	public String getFavoriteSubject() {
		return favoriteSubject;
	}
	public void setFavoriteSubject(String favoriteSubject) {
		this.favoriteSubject = favoriteSubject;
	}
	public Integer getFavoriteSubjectMemberCount() {
		return favoriteSubjectMemberCount;
	}
	public void setFavoriteSubjectMemberCount(Integer favoriteSubjectMemberCount) {
		this.favoriteSubjectMemberCount = favoriteSubjectMemberCount;
	}
	public Integer getGroupMemberNum() {
		return groupMemberNum;
	}
	public void setGroupMemberNum(Integer groupMemberNum) {
		this.groupMemberNum = groupMemberNum;
	}
	
}
