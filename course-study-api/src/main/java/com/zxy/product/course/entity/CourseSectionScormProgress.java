package com.zxy.product.course.entity;


import com.zxy.product.course.jooq.tables.pojos.CourseSectionScormProgressEntity;

/**
 * Created by keeley on 2017/6/5.
 */
public class CourseSectionScormProgress extends CourseSectionScormProgressEntity {
    public static final int STATUS_FINISH = 2;
    public static final int STATUS_STUDY = 1;
    public static final int STATUS_NO = 0;
    private static final long serialVersionUID = -3454315414667431028L;
    private String executeType;
    private Integer clientType;
    private String sectionLogId;

    public String getExecuteType() {
        return executeType;
    }

    public void setExecuteType(String executeType) {
        this.executeType = executeType;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getSectionLogId() {
        return sectionLogId;
    }

    public void setSectionLogId(String sectionLogId) {
        this.sectionLogId = sectionLogId;
    }
}
