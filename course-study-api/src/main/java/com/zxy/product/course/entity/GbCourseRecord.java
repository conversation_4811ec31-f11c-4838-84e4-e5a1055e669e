package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbCourseRecordEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbCourseRecord extends GbCourseRecordEntity {
    private static final long serialVersionUID = -816118364390813626L;

    //审核状态 0：待审核 1：已审核 2：未通过 3：待评价 4：已完成 5：已取消
    public static final int AUDIT_ZERO = 0;
    public static final int ONE = 1;
    public static final int TWO = 2;

    public static final int AUDIT_E = 3;
    public static final int AUDIT_L = 4;
    public static final int AUDIT_C = 5;

    /**
     * 显示状态
     * 0:显示 1:不显示
     */
    public static final int SHOW_YES = 0;
    public static final int SHOW_NO = 1;


    public static final String LECTURER_SATISFIED_SYMBOL = "-";

    private int row;
    private List<RowError> errors = new ArrayList<>();
    //课程编码
    private String courseMiddleCode;
    //授课日期字符串
    private String courseDateStr;
    //约课时间字符串
    private String createBeginDateStr;
    //约课人员工编号
    private String memberName;

    public static final Integer TEMPLATE_COLUMN_COURSE_MIDDLE_CODE = 0; //课程关联编码
    public static final Integer TEMPLATE_COLUMN_CLASS_NAME = 1;  //培训班名称
    public static final Integer TEMPLATE_COLUMN_COURSE_DATE = 2; //授课日期
    public static final Integer TEMPLATE_COLUMN_COURSE_TIME = 3; //授课时长
    public static final Integer TEMPLATE_COLUMN_COURSE_LOCATION = 4; //授课地点
    public static final Integer TEMPLATE_COLUMN_COURSE_OBJECT = 5;  //授课对象
    public static final Integer TEMPLATE_COLUMN_NUM = 6; //培训人数
    public static final Integer TEMPLATE_COLUMN_LECTURER_SATISFIED = 7; //授课满意度
    public static final Integer TEMPLATE_COLUMN_MEMBER_NAME = 8;  //约课人员工编号
    public static final Integer TEMPLATE_COLUMN_CREATE_BEGIN_DATE = 9;  //约课时间

    public static final String TEMPLATE_COLUMN_COURSE_CODE_CN = "课程编码(必填)";
    public static final String TEMPLATE_COLUMN_CLASS_NAME_CN = "培训班名称(必填)";
    public static final String TEMPLATE_COLUMN_COURSE_DATE_CN = "授课日期(必填)";
    public static final String TEMPLATE_COLUMN_COURSE_TIME_CN = "授课时长(必填)";
    public static final String TEMPLATE_COLUMN_COURSE_LOCATION_CN = "授课地点(必填)";
    public static final String TEMPLATE_COLUMN_COURSE_OBJECT_CN = "授课对象(必填)";
    public static final String TEMPLATE_COLUMN_NUM_CN = "培训人数(必填)";
    public static final String TEMPLATE_COLUMN_LECTURER_SATISFIED_CN = "授课满意度(非必填)";
    public static final String TEMPLATE_COLUMN_MEMBER_NAME_CN = "约课人员工编号(必填)";
    public static final String TEMPLATE_COLUMN_CREATE_BEGIN_DATE_CN = "约课时间(必填)";



    GbCourseLibrary gbCourseLibrary;
    GbCourseClassification gbCourseClassification;
    GbLecturerLibrary gbLecturerLibrary;
    Organization organization;
    Member member;
    public String jobId;
    public String jobName;

    public GbCourseLibrary getGbCourseLibrary() {
        return gbCourseLibrary;
    }

    public void setGbCourseLibrary(GbCourseLibrary gbCourseLibrary) {
        this.gbCourseLibrary = gbCourseLibrary;
    }

    public GbCourseClassification getGbCourseClassification() {
        return gbCourseClassification;
    }

    public void setGbCourseClassification(GbCourseClassification gbCourseClassification) {
        this.gbCourseClassification = gbCourseClassification;
    }

    public GbLecturerLibrary getGbLecturerLibrary() {
        return gbLecturerLibrary;
    }

    public void setGbLecturerLibrary(GbLecturerLibrary gbLecturerLibrary) {
        this.gbLecturerLibrary = gbLecturerLibrary;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public String getCourseMiddleCode() {
        return courseMiddleCode;
    }

    public void setCourseMiddleCode(String courseMiddleCode) {
        this.courseMiddleCode = courseMiddleCode;
    }

    public String getCourseDateStr() {
        return courseDateStr;
    }

    public void setCourseDateStr(String courseDateStr) {
        this.courseDateStr = courseDateStr;
    }

    public String getCreateBeginDateStr() {
        return createBeginDateStr;
    }

    public void setCreateBeginDateStr(String createBeginDateStr) {
        this.createBeginDateStr = createBeginDateStr;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }
}
