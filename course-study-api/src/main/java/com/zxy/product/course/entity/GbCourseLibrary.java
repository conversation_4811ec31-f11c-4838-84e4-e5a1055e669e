package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbCourseLibraryEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbCourseLibrary extends GbCourseLibraryEntity {
    private static final long serialVersionUID = 4268335015438348416L;

    CourseInfo courseInfo;
    List<GbCourseClassification> gbCourseClassificationList;
    List<GbLecturerLibrary> gbLecturerLibraryList;

    boolean auditStatus;
    public static final String TEMPLATE_COURSE_NAME_CN = "课程名称(必填)";
    public static final String TEMPLATE_OBJECT_ORIENTED_CN = "面向对象(非必填)";
    public static final String TEMPLATE_INTRODUCTION_CN = "内容简介(非必填)";
    public static final String TEMPLATE_OUTLINE_CN = "大纲(非必填)";

    private int row;
    private List<RowError> errors = new ArrayList<>();
    public static final Integer TEMPLATE_COURSE_NAME_COLUMN = 0;
    public static final Integer TEMPLATE_OBJECT_ORIENTED_COLUMN = 1;
    public static final Integer TEMPLATE_INTRODUCTION_COLUMN = 2;
    public static final Integer TEMPLATE_OUTLINE_COLUMN = 3;
    /**
     * 导入限制条数
     */
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;


    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public List<GbCourseClassification> getGbCourseClassificationList() {
        return gbCourseClassificationList;
    }

    public void setGbCourseClassificationList(List<GbCourseClassification> gbCourseClassificationList) {
        this.gbCourseClassificationList = gbCourseClassificationList;
    }

    public List<GbLecturerLibrary> getGbLecturerLibraryList() {
        return gbLecturerLibraryList;
    }

    public void setGbLecturerLibraryList(List<GbLecturerLibrary> gbLecturerLibraryList) {
        this.gbLecturerLibraryList = gbLecturerLibraryList;
    }

    public boolean isAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(boolean auditStatus) {
        this.auditStatus = auditStatus;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }
}
