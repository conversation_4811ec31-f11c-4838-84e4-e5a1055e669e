package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogAhDayEntity;
import org.jooq.Record;
import org.jooq.impl.TableImpl;

public class CourseSectionStudyLogAhDay extends CourseSectionStudyLogAhDayEntity{

	private String courseName;
	private Integer courseStatus;
	private String topicName; //标签名称
	private Integer studyAvgTime; //平均学时
	private Integer topicNum; //标签数量

	/**
	 *
	 */
	private static final long serialVersionUID = -1599919186641546079L;

	public CourseSectionStudyLogAhDay fill(TableImpl<?> table, Record r) {
		this.setId(r.get(table.field("f_id"), String.class));
		this.setMemberId(r.get(table.field("f_member_id"), String.class));
		this.setCourseId(r.get(table.field("f_course_id"), String.class));
		this.setAppStudyTime(r.get(table.field("f_app_study_time"), Integer.class));
		this.setPcStudyTime(r.get(table.field("f_pc_study_time"), Integer.class));
		this.setStudyTime(r.get(table.field("f_study_time"), Integer.class));
		this.setDay(r.get(table.field("f_day"), Integer.class));
		this.setMonth(r.get(table.field("f_month"), Integer.class));
		this.setYear(r.get(table.field("f_year"), Integer.class));
		this.setCreateTime(r.get(table.field("f_create_time"), Long.class));
		return this;
	}

	public String getCourseName() {
		return courseName;
	}

	public CourseSectionStudyLogAhDay setCourseName(String courseName) {
		this.courseName = courseName;
		return this;
	}

	public Integer getCourseStatus() {
		return courseStatus;
	}

	public CourseSectionStudyLogAhDay setCourseStatus(Integer courseStatus) {
		this.courseStatus = courseStatus;
		return this;
	}

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Integer getStudyAvgTime() {
        return studyAvgTime;
    }

    public void setStudyAvgTime(Integer studyAvgTime) {
        this.studyAvgTime = studyAvgTime;
    }

    public Integer getTopicNum() {
        return topicNum;
    }

    public void setTopicNum(Integer topicNum) {
        this.topicNum = topicNum;
    }
}
