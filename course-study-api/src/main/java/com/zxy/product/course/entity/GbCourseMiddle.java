package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbCourseMiddleEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbCourseMiddle extends GbCourseMiddleEntity {
    private static final long serialVersionUID = -910111335345063408L;

    GbCourseLibrary gbCourseLibrary;
    GbLecturerLibrary gbLecturerLibrary;
    GbCourseClassification gbCourseClassification;
    CourseInfo courseInfo;

    String courseCode;
    String lecturerCode;
    String classificationCode;

    public static final Integer TEMPLATE_COURSE_CODE_COLUMN = 0;
    public static final Integer TEMPLATE_CLASSIFICATION_CODE_COLUMN = 1;
    public static final Integer TEMPLATE_LECTURER_CODE_COLUMN = 2;

    private int row;
    private List<RowError> errors = new ArrayList<>();

    public static final String TEMPLATE_COURSE_CODE_CN = "课程编码(必填)";
    public static final String TEMPLATE__CLASSIFICATION_CODE_CN = "课程分类编码(必填)";
    public static final String TEMPLATE_LECTURER_CODE_CN = "讲师编码(必填)";

    public GbCourseLibrary getGbCourseLibrary() {
        return gbCourseLibrary;
    }

    public void setGbCourseLibrary(GbCourseLibrary gbCourseLibrary) {
        this.gbCourseLibrary = gbCourseLibrary;
    }

    public GbLecturerLibrary getGbLecturerLibrary() {
        return gbLecturerLibrary;
    }

    public void setGbLecturerLibrary(GbLecturerLibrary gbLecturerLibrary) {
        this.gbLecturerLibrary = gbLecturerLibrary;
    }

    public GbCourseClassification getGbCourseClassification() {
        return gbCourseClassification;
    }

    public void setGbCourseClassification(GbCourseClassification gbCourseClassification) {
        this.gbCourseClassification = gbCourseClassification;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getLecturerCode() {
        return lecturerCode;
    }

    public void setLecturerCode(String lecturerCode) {
        this.lecturerCode = lecturerCode;
    }

    public String getClassificationCode() {
        return classificationCode;
    }

    public void setClassificationCode(String classificationCode) {
        this.classificationCode = classificationCode;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }
}
