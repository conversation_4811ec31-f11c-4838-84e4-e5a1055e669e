package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.DeleteDataCourseEntity;
import org.jooq.impl.TableImpl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public class DeleteDataCourse extends DeleteDataCourseEntity {
    /**
     * wentao   修改的地方表名并未维护常量 而是根据jooq获取的表名
     */
    private static final String DATABASE_NASE = "course_study";

    public static final String COURSE_INFO = "t_course_info";
    public static final String AUDIENCE_ITEM = "t_audience_item";
    public static final String AUDIENCE_OBJECT = "t_audience_object";
    public static final String AUDIENCE_MEMBER = "t_audience_member";
    public static final String COURSE_CHAPTER_SECTION = "t_course_chapter_section";
    public static final String COURSE_SECTION_STUDY_LOG = "t_course_section_study_log";
    public static final String COURSE_SECTION_STUDY_LOG_AH = "t_course_section_study_log_ah";
    public static final String COURSE_SECTION_STUDY_LOG_AH_DAY = "t_course_section_study_log_ah_day";
    public static final String COURSE_SECTION_STUDY_LOG_BJ = "t_course_section_study_log_bj";
    public static final String COURSE_SECTION_STUDY_LOG_BJ_DAY = "t_course_section_study_log_bj_day";
    public static final String COURSE_SECTION_STUDY_LOG_CM = "t_course_section_study_log_cm";
    public static final String COURSE_SECTION_STUDY_LOG_CM_DAY = "t_course_section_study_log_cm_day";
    public static final String COURSE_SECTION_STUDY_LOG_CQ = "t_course_section_study_log_cq";
    public static final String COURSE_SECTION_STUDY_LOG_CQ_DAY = "t_course_section_study_log_cq_day";
    public static final String COURSE_SECTION_STUDY_LOG_EB = "t_course_section_study_log_eb";
    public static final String COURSE_SECTION_STUDY_LOG_EB_DAY = "t_course_section_study_log_eb_day";
    public static final String COURSE_SECTION_STUDY_LOG_FJ = "t_course_section_study_log_fj";
    public static final String COURSE_SECTION_STUDY_LOG_FJ_DAY = "t_course_section_study_log_fj_day";
    public static final String COURSE_SECTION_STUDY_LOG_GD = "t_course_section_study_log_gd";
    public static final String COURSE_SECTION_STUDY_LOG_GD_DAY = "t_course_section_study_log_gd_day";
    public static final String COURSE_SECTION_STUDY_LOG_GS = "t_course_section_study_log_gs";
    public static final String COURSE_SECTION_STUDY_LOG_GS_DAY = "t_course_section_study_log_gs_day";
    public static final String COURSE_SECTION_STUDY_LOG_GX = "t_course_section_study_log_gx";
    public static final String COURSE_SECTION_STUDY_LOG_GX_DAY = "t_course_section_study_log_gx_day";
    public static final String COURSE_SECTION_STUDY_LOG_GZ = "t_course_section_study_log_gz";
    public static final String COURSE_SECTION_STUDY_LOG_GZ_DAY = "t_course_section_study_log_gz_day";
    public static final String COURSE_SECTION_STUDY_LOG_HB = "t_course_section_study_log_hb";
    public static final String COURSE_SECTION_STUDY_LOG_HB_DAY = "t_course_section_study_log_hb_day";
    public static final String COURSE_SECTION_STUDY_LOG_HL = "t_course_section_study_log_hl";
    public static final String COURSE_SECTION_STUDY_LOG_HL_DAY = "t_course_section_study_log_hl_day";
    public static final String COURSE_SECTION_STUDY_LOG_HN = "t_course_section_study_log_hn";
    public static final String COURSE_SECTION_STUDY_LOG_HN_DAY = "t_course_section_study_log_hn_day";
    public static final String COURSE_SECTION_STUDY_LOG_JL = "t_course_section_study_log_jl";
    public static final String COURSE_SECTION_STUDY_LOG_JL_DAY = "t_course_section_study_log_jl_day";
    public static final String COURSE_SECTION_STUDY_LOG_JS = "t_course_section_study_log_js";
    public static final String COURSE_SECTION_STUDY_LOG_JS_DAY = "t_course_section_study_log_js_day";
    public static final String COURSE_SECTION_STUDY_LOG_JX = "t_course_section_study_log_jx";
    public static final String COURSE_SECTION_STUDY_LOG_JX_DAY = "t_course_section_study_log_jx_day";
    public static final String COURSE_SECTION_STUDY_LOG_LN = "t_course_section_study_log_ln";
    public static final String COURSE_SECTION_STUDY_LOG_LN_DAY = "t_course_section_study_log_ln_day";
    public static final String COURSE_SECTION_STUDY_LOG_NM = "t_course_section_study_log_nm";
    public static final String COURSE_SECTION_STUDY_LOG_NM_DAY = "t_course_section_study_log_nm_day";
    public static final String COURSE_SECTION_STUDY_LOG_NX = "t_course_section_study_log_nx";
    public static final String COURSE_SECTION_STUDY_LOG_NX_DAY = "t_course_section_study_log_nx_day";
    public static final String COURSE_SECTION_STUDY_LOG_OTHER = "t_course_section_study_log_other";
    public static final String COURSE_SECTION_STUDY_LOG_OTHER_DAY = "t_course_section_study_log_other_day";
    public static final String COURSE_SECTION_STUDY_LOG_QH = "t_course_section_study_log_qh";
    public static final String COURSE_SECTION_STUDY_LOG_QH_DAY = "t_course_section_study_log_qh_day";
    public static final String COURSE_SECTION_STUDY_LOG_QO = "t_course_section_study_log_qo";
    public static final String COURSE_SECTION_STUDY_LOG_QO_DAY = "t_course_section_study_log_qo_day";
    public static final String COURSE_SECTION_STUDY_LOG_SC = "t_course_section_study_log_sc";
    public static final String COURSE_SECTION_STUDY_LOG_SC_DAY = "t_course_section_study_log_sc_day";
    public static final String COURSE_SECTION_STUDY_LOG_SD = "t_course_section_study_log_sd";
    public static final String COURSE_SECTION_STUDY_LOG_SD_DAY = "t_course_section_study_log_sd_day";
    public static final String COURSE_SECTION_STUDY_LOG_SH = "t_course_section_study_log_sh";
    public static final String COURSE_SECTION_STUDY_LOG_SH_DAY = "t_course_section_study_log_sh_day";
    public static final String COURSE_SECTION_STUDY_LOG_SN = "t_course_section_study_log_sn";
    public static final String COURSE_SECTION_STUDY_LOG_SN_DAY = "t_course_section_study_log_sn_day";
    public static final String COURSE_SECTION_STUDY_LOG_SX = "t_course_section_study_log_sx";
    public static final String COURSE_SECTION_STUDY_LOG_SX_DAY = "t_course_section_study_log_sx_day";
    public static final String COURSE_SECTION_STUDY_LOG_TJ = "t_course_section_study_log_tj";
    public static final String COURSE_SECTION_STUDY_LOG_TJ_DAY = "t_course_section_study_log_tj_day";
    public static final String COURSE_SECTION_STUDY_LOG_XJ = "t_course_section_study_log_xj";
    public static final String COURSE_SECTION_STUDY_LOG_XJ_DAY = "t_course_section_study_log_xj_day";
    public static final String COURSE_SECTION_STUDY_LOG_XN = "t_course_section_study_log_xn";
    public static final String COURSE_SECTION_STUDY_LOG_XN_DAY = "t_course_section_study_log_xn_day";
    public static final String COURSE_SECTION_STUDY_LOG_XZ = "t_course_section_study_log_xz";
    public static final String COURSE_SECTION_STUDY_LOG_XZ_DAY = "t_course_section_study_log_xz_day";
    public static final String COURSE_SECTION_STUDY_LOG_YN = "t_course_section_study_log_yn";
    public static final String COURSE_SECTION_STUDY_LOG_YN_DAY = "t_course_section_study_log_yn_day";
    public static final String COURSE_SECTION_STUDY_LOG_ZGTT = "t_course_section_study_log_zgtt";
    public static final String COURSE_SECTION_STUDY_LOG_ZGTT_DAY = "t_course_section_study_log_zgtt_day";
    public static final String COURSE_SECTION_STUDY_LOG_ZJ = "t_course_section_study_log_zj";
    public static final String COURSE_SECTION_STUDY_LOG_ZJ_DAY = "t_course_section_study_log_zj_day";
    public static final String COURSE_SECTION_STUDY_LOG_ZX = "t_course_section_study_log_zx";
    public static final String COURSE_SECTION_STUDY_LOG_ZX_DAY = "t_course_section_study_log_zx_day";
    public static final String COURSE_SECTION_STUDY_PROGRESS = "t_course_section_study_progress";
    public static final String COURSE_SECTION_STUDY_PROGRESS_CHBNC = "t_course_section_study_progress_chbnc";
    public static final String COURSE_SECTION_STUDY_PROGRESS_CHBNS = "t_course_section_study_progress_chbns";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLC = "t_course_section_study_progress_ffclc";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022 = "t_course_section_study_progress_ffclc_2022";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023 = "t_course_section_study_progress_ffclc_2023";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLS = "t_course_section_study_progress_ffcls";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022 = "t_course_section_study_progress_ffcls_2022";
    public static final String COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023 = "t_course_section_study_progress_ffcls_2023";
    public static final String COURSE_SECTION_STUDY_PROGRESS_RTS = "t_course_section_study_progress_rts";
    public static final String COURSE_SECTION_STUDY_PROGRESS_XDNC = "t_course_section_study_progress_xdnc";
    public static final String COURSE_SECTION_STUDY_PROGRESS_XDNS = "t_course_section_study_progress_xdns";
    public static final String COURSE_SECTION_STUDY_PROGRESS_ZHZTS = "t_course_section_study_progress_zhzts";
    public static final String COURSE_CATEGORY = "t_course_category";
    public static final String GENSEE_BUSINESS = "t_gensee_business";
    public static final String GENSEE_USER_JOIN_HISTORY = "t_gensee_user_join_history";
    public static final String KNOWLEDGE_INFO = "t_knowledge_info";
    public static final String BUSINESS_TOPIC = "t_business_topic";
    public static final String CERTIFICATE_RECORD = "t_certificate_record";

    /**
     * 分表的表名写起来麻烦 干脆这样
     * 包装一层的意义不大，就是想写点注释
     * @param table
     * @return
     */
    public static String getTableName(TableImpl<?> table){
        return Objects.nonNull(table) ? table.getName() : "other";
    }

    public static DeleteDataCourse getDeleteDataCourse(String tableName, String businessId, String companyId){
        DeleteDataCourse deleteDataCourse = new DeleteDataCourse();
        deleteDataCourse.setDatabaseName(DATABASE_NASE);
        deleteDataCourse.setBusinessId(businessId);
        deleteDataCourse.setTableName(tableName);
        deleteDataCourse.setBusinessId(businessId);
        deleteDataCourse.setCompanyId(companyId);
        deleteDataCourse.forInsert();
        return deleteDataCourse;
    }
    public static List<DeleteDataCourse> getDeleteDataCourseList(String tableName, List<String> businessIds, String companyId) {
        return businessIds.stream().map(id -> getDeleteDataCourse(tableName, id, companyId)).collect(Collectors.toList());
    }

}
