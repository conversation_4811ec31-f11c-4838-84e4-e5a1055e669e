package com.zxy.product.course.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> zhouyong
 * @ClassName : CourseStudy30daysStatistics
 * @Description : 最近30天学习数据
 * @date : 2021-10-25 16:32
 */
public class CourseStudy30daysStatistics implements Serializable {

    private static final long serialVersionUID = 8422584433280607167L;

    private String  name;                   //组织名称
    private String  orgShortName;           //组织名称
    private String  orgId;                  //组织ID
    private long    count;                  //学习人次
    private long    number;                 //学习人数
    private long    totalLengthTime;        //学习总时长 h
    private long    avgLengthTime;          //人均时长
    private double  activityRatio;          //学习活跃度
    private long    lengthTimeSeconds;      //学习总时长 s

    public CourseStudy30daysStatistics() {}

    public CourseStudy30daysStatistics(String name) {
        this.name = name;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    public CourseStudy30daysStatistics(long count, long number, long totalLengthTime) {
        this.count = count;
        this.number = number;
        this.lengthTimeSeconds = totalLengthTime;
        this.totalLengthTime = new BigDecimal(String.valueOf(totalLengthTime/3600.0)).setScale(0,BigDecimal.ROUND_HALF_UP).longValue();
    }

    public String getName() {
        return name;
    }

    public CourseStudy30daysStatistics setName(String name) {
        this.name = name;
        return this;
    }

    public String getOrgId() {
        return orgId;
    }

    public CourseStudy30daysStatistics setOrgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public long getTotalLengthTime() {
        return totalLengthTime;
    }

    public void setTotalLengthTime(long totalLengthTime) {
        this.totalLengthTime = totalLengthTime;
    }

    public long getAvgLengthTime() {
        return avgLengthTime;
    }

    public void setAvgLengthTime(long avgLengthTime) {
        this.avgLengthTime = avgLengthTime;
    }

    public double getActivityRatio() {
        return activityRatio;
    }

    public void setActivityRatio(double activityRatio) {
        this.activityRatio = activityRatio;
    }

    public CourseStudy30daysStatistics setAvgLengthTime() {
        this.avgLengthTime = this.number == 0 ? 0 : new BigDecimal(String.valueOf((double) this.totalLengthTime/this.number)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
        return this;
    }

    public CourseStudy30daysStatistics setActivityRatio(int totalCount) {
        this.activityRatio = totalCount == 0 ? 0.00 : new BigDecimal(String.valueOf((double) this.number/totalCount)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        return this;
    }

    public long getLengthTimeSeconds() {
        return lengthTimeSeconds;
    }

    public void setLengthTimeSeconds(long lengthTimeSeconds) {
        this.lengthTimeSeconds = lengthTimeSeconds;
    }

    @Override
    public String toString() {
        return "CourseStudy30daysStatistics{" +
            "name='" + name + '\'' +
            ", orgId=" + orgId +
            ", count=" + count +
            ", number=" + number +
            ", totalLengthTime=" + totalLengthTime +
            ", avgLengthTime=" + avgLengthTime +
            ", activityRatio=" + activityRatio +
            ", lengthTimeSeconds=" + lengthTimeSeconds +
            '}';
    }
}
