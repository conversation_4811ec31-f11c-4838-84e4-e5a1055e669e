package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.KnowledgeCategoryEntity;

/**
 * Created by <PERSON> on 2017/3/6.
 */
public class KnowledgeCategory extends KnowledgeCategoryEntity {
    private static final long serialVersionUID = -340385233881532868L;

    public static final String KNOWLEDGE_CATEGORY_URI = "course-study/knowledge-category";

    public static final Integer STATE_ENABLE = 1;// 状态:启用
    public static final Integer STATE_DISABLE = 2;// 状态:禁用
    
    public static final int HIDE_NO = 0;
    public static final int HIDE_YES = 1;
    
	public static final String KNOWLEDGE_CATEGORY_ORGANIZATION_DEC = "部门";
	public static final String KNOWLEDGE_CATEGORY_CODE = "部门编码";

    // 父目录
    private KnowledgeCategory parent;

    private Organization organization;

    private Integer knowledgeCount;
    
    private Integer Index;
    
	public Integer getIndex() {
		return Index;
	}

	public void setIndex(Integer index) {
		Index = index;
	}

	public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }


    public KnowledgeCategory getParent() {
        return parent;
    }

    public void setParent(KnowledgeCategory parent) {
        this.parent = parent;
    }

    public Integer getCourseCount() {
        return knowledgeCount;
    }

    public void setCourseCount(Integer knowledgeCount) {
        this.knowledgeCount = knowledgeCount;
    }
}
