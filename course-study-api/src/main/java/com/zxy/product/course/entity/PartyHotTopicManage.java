package com.zxy.product.course.entity;


import com.zxy.product.course.jooq.tables.pojos.PartyHotTopicManageEntity;

/**
 * <AUTHOR>
 */
public class PartyHotTopicManage extends PartyHotTopicManageEntity {

    public static final Integer STATUS_DELETE = 2;
    public static final Integer STATUS_ENABLED = 1;
    public static final Integer STATUS_DISABLED = 0;
    private static final long serialVersionUID = 907462127293539309L;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签类型id
     */
    private String typeId;

    /**
     * 标签类型
     */
    private String typeName;

    /**
     * 标签属性 0=临时标签,1=正式标签
     */
    private Integer group;

    /**
     * 标签热度
     */
    private Integer courseVisits;

    /**
     * 热度排名
     */
    private Integer hotRank;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getGroup() {
        return group;
    }

    public void setGroup(Integer group) {
        this.group = group;
    }

    public Integer getCourseVisits() {
        return courseVisits;
    }

    public void setCourseVisits(Integer courseVisits) {
        this.courseVisits = courseVisits;
    }

    public Integer getHotRank() {
        return hotRank;
    }

    public void setHotRank(Integer hotRank) {
        this.hotRank = hotRank;
    }
}
