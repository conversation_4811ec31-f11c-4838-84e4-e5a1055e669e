package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.MultidimensionalScoringEntity;

public class MultidimensionalScoring extends MultidimensionalScoringEntity {

    public static final String URI = "course-study/scoring-info";
    public static final String NOT_SCORE="/";

    private String memberName;//创建人
    private String orgName;//归属部门
    private String topicId;
    private String subjectId;

    private String themeName; //主题名称
    private Integer courseScoreNumber;//课程评分总人数
    private String firstLatitudeTotalScore; //第一维度总分数
    private String secondLatitudeTotalScore; //第二维度总分数
    private String thirdLatitudeTotalScore; //第三维度总分数
    private String fourthLatitudeTotalScore; //第四维度总分数
    private String fifthLatitudeTotalScore; //第五维度总分数

    private String courseChapterId;//主题id

    private Integer  firstLatitudeTotalScoreCount;//第一维度总评分人数
    private Integer secondLatitudeTotalScoreCount; //第二维度总评分人数
    private Integer thirdLatitudeTotalScoreCount; //第三维度总评分人数
    private Integer fourthLatitudeTotalScoreCount; //第四维度总评分人数
    private Integer fifthLatitudeTotalScoreCount; //第五维度总评分人数


    public void setCourseChapterId(String courseChapterId) {
        this.courseChapterId = courseChapterId;
    }

    public String getCourseChapterId() {
        return courseChapterId;
    }

    public Integer getFirstLatitudeTotalScoreCount() {
        return firstLatitudeTotalScoreCount;
    }

    public void setFirstLatitudeTotalScoreCount(Integer firstLatitudeTotalScoreCount) {
        this.firstLatitudeTotalScoreCount = firstLatitudeTotalScoreCount;
    }

    public Integer getSecondLatitudeTotalScoreCount() {
        return secondLatitudeTotalScoreCount;
    }

    public void setSecondLatitudeTotalScoreCount(Integer secondLatitudeTotalScoreCount) {
        this.secondLatitudeTotalScoreCount = secondLatitudeTotalScoreCount;
    }

    public Integer getThirdLatitudeTotalScoreCount() {
        return thirdLatitudeTotalScoreCount;
    }

    public void setThirdLatitudeTotalScoreCount(Integer thirdLatitudeTotalScoreCount) {
        this.thirdLatitudeTotalScoreCount = thirdLatitudeTotalScoreCount;
    }

    public Integer getFourthLatitudeTotalScoreCount() {
        return fourthLatitudeTotalScoreCount;
    }

    public void setFourthLatitudeTotalScoreCount(Integer fourthLatitudeTotalScoreCount) {
        this.fourthLatitudeTotalScoreCount = fourthLatitudeTotalScoreCount;
    }

    public Integer getFifthLatitudeTotalScoreCount() {
        return fifthLatitudeTotalScoreCount;
    }

    public void setFifthLatitudeTotalScoreCount(Integer fifthLatitudeTotalScoreCount) {
        this.fifthLatitudeTotalScoreCount = fifthLatitudeTotalScoreCount;
    }

    private String courseName;//课程名

    private String courseId; //课程id

    private String chapterName;//主题名称

    private Integer order;

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public Integer getCourseScoreNumber() {
        return courseScoreNumber;
    }

    public void setCourseScoreNumber(Integer courseScoreNumber) {
        this.courseScoreNumber = courseScoreNumber;
    }

    public String getFirstLatitudeTotalScore() {
        return firstLatitudeTotalScore;
    }

    public void setFirstLatitudeTotalScore(String firstLatitudeTotalScore) {
        this.firstLatitudeTotalScore = firstLatitudeTotalScore;
    }

    public String getSecondLatitudeTotalScore() {
        return secondLatitudeTotalScore;
    }

    public void setSecondLatitudeTotalScore(String secondLatitudeTotalScore) {
        this.secondLatitudeTotalScore = secondLatitudeTotalScore;
    }

    public String getThirdLatitudeTotalScore() {
        return thirdLatitudeTotalScore;
    }

    public void setThirdLatitudeTotalScore(String thirdLatitudeTotalScore) {
        this.thirdLatitudeTotalScore = thirdLatitudeTotalScore;
    }

    public String getFourthLatitudeTotalScore() {
        return fourthLatitudeTotalScore;
    }

    public void setFourthLatitudeTotalScore(String fourthLatitudeTotalScore) {
        this.fourthLatitudeTotalScore = fourthLatitudeTotalScore;
    }

    public String getFifthLatitudeTotalScore() {
        return fifthLatitudeTotalScore;
    }

    public void setFifthLatitudeTotalScore(String fifthLatitudeTotalScore) {
        this.fifthLatitudeTotalScore = fifthLatitudeTotalScore;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getTopicId() {
        return topicId;
    }

    public MultidimensionalScoring setTopicId(String topicId) {
        this.topicId = topicId;
        return this;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public MultidimensionalScoring setSubjectId(String subjectId) {
        this.subjectId = subjectId;
        return this;
    }
}
