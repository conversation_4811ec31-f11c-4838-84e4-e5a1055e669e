package com.zxy.product.course.entity;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrgStudyActivityStatistics implements Serializable {

    private static final long serialVersionUID = -2482703858511216247L;

    private String  orgName;                        //组织名称
    private String  orgShortName;                   //组织名称
    private Integer studyVisits;                    //学习人次
    private Integer studyCounts;                    //学习人数
    private Integer  participateRatio;               //参与率
    private Integer  finishedRatio;                  //完成率

    private Integer finishedCounts;                 //学习完成账号数

    public OrgStudyActivityStatistics() {
    }

    public OrgStudyActivityStatistics(String orgName, Integer studyVisits, Integer studyCounts) {
        this.orgName = orgName;
        this.studyVisits = studyVisits;
        this.studyCounts = studyCounts;
    }


    public OrgStudyActivityStatistics(String orgName,String orgShortName, Integer studyVisits, Integer studyCounts) {
        this.orgName = orgName;
        this.orgShortName = orgShortName;
        this.studyVisits = studyVisits;
        this.studyCounts = studyCounts;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getStudyVisits() {
        return studyVisits;
    }

    public void setStudyVisits(Integer studyVisits) {
        this.studyVisits = studyVisits;
    }

    public Integer getStudyCounts() {
        return studyCounts;
    }

    public void setStudyCounts(Integer studyCounts) {
        this.studyCounts = studyCounts;
    }

    public Integer getParticipateRatio() {
        return participateRatio;
    }

    public void setParticipateRatio(Integer participateRatio) {
        this.participateRatio = participateRatio;
    }

    public Integer getFinishedCounts() {
        return finishedCounts;
    }

    public void setFinishedCounts(Integer finishedCounts) {
        this.finishedCounts = finishedCounts;
    }

    public OrgStudyActivityStatistics setParticipateRatio(Integer studyCount, Integer rangeCount) {
        if (null == rangeCount || rangeCount == 0) {
            this.participateRatio = 0;
            return this;
        }
        this.participateRatio = new BigDecimal(String.valueOf(studyCount.doubleValue()/rangeCount)).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue();
        return this;
    }

    public Integer getFinishedRatio() {
        return finishedRatio;
    }

    public void setFinishedRatio(Integer finishedRatio) {
        this.finishedRatio = finishedRatio;
    }

    public OrgStudyActivityStatistics setFinishedRatio(Integer orgFinishedCount,Integer rangeCount) {
        if (null == rangeCount || rangeCount == 0) {
            this.finishedRatio = 0;
            return this;
        }
        this.finishedRatio = new BigDecimal(String.valueOf(orgFinishedCount.doubleValue()/rangeCount)).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue();
        return this;
    }
}