package com.zxy.product.course.content;

import org.jooq.Field;
import org.jooq.Table;

import static com.zxy.product.course.jooq.Tables.*;

/**
 * @Author: liga<PERSON>qi<PERSON>
 * @Date: 2025/6/9 16:19
 * @Description: 报表兑换积分明细描述业务类
 */
public enum PointBusinessTypeEnum {

    COURSE(1, "兑换课程", COURSE_INFO.NAME, COURSE_INFO),
    LIVE(2, "兑换直播", GENSEE_WEB_CAST.SUBJECT, GENSEE_WEB_CAST),
    KNOWLEDGE(3, "下载知识", KNOWLEDGE_INFO.NAME, KNOWLEDGE_INFO);

    private final int type;
    private final String desc;
    private final Field<String> field;
    private final Table<?> table;

    PointBusinessTypeEnum(int type, String desc, Field<String> field, Table<?> table) {
        this.type = type;
        this.desc = desc;
        this.field = field;
        this.table = table;
    }

    public static PointBusinessTypeEnum of(int type) {
        for (PointBusinessTypeEnum b : values()) {
            if (b.type == type) {
                return b;
            }
        }
        return LIVE;
    }

    public String getDesc() { return desc; }
    public Field<String> getField() { return field; }
    public Table<?> getTable() { return table; }
}
