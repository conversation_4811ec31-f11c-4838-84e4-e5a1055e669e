package com.zxy.product.course.entity;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON> on 2017/3/8.
 */
public class GenseeHistoryResult implements Serializable {
    private static final long serialVersionUID = 8764056221203927758L;
    private String code;// 返回结果代码
    private String message;// 结果说明
    private List<GenseeUserHistoryResult> list;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<GenseeUserHistoryResult> getList() {
        return list;
    }

    public void setList(List<GenseeUserHistoryResult> list) {
        this.list = list;
    }
}
