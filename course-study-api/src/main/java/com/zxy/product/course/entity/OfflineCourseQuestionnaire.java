package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.OfflineCourseQuestionnaireEntity;

import java.util.List;

/**
 * 线下课程调查问卷表
 *
 * <AUTHOR>
 * @date 2021/3/5/0005 15:15
 */
public class OfflineCourseQuestionnaire extends OfflineCourseQuestionnaireEntity {

    private static final long serialVersionUID = 5954164998453807948L;


    private Member member;
    private List<OfflineCourseQuestionnaireChapter> chapterList;
    private List<QuestionnaireMouldQuestion> otherQuestionList;
    private Statistics otherStatistics;
    private Integer count;


    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }


    public List<OfflineCourseQuestionnaireChapter> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<OfflineCourseQuestionnaireChapter> chapterList) {
        this.chapterList = chapterList;
    }

    public List<QuestionnaireMouldQuestion> getOtherQuestionList() {
        return otherQuestionList;
    }

    public void setOtherQuestionList(List<QuestionnaireMouldQuestion> otherQuestionList) {
        this.otherQuestionList = otherQuestionList;
    }

    public Statistics getOtherStatistics() {
        return otherStatistics;
    }

    public void setOtherStatistics(Statistics otherStatistics) {
        this.otherStatistics = otherStatistics;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
