package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeSubscriptionEntity;

/**
 * Created by <PERSON> on 2017/3/3.
 */
public class GenseeSubscription extends GenseeSubscriptionEntity {


    private static final long serialVersionUID = 3880580822984031130L;

    /** 是否订阅 0未订阅 */
    public static final int GENSEE_SUB_STATUS_UN = 0;
    /** 是否订阅 1已订阅 */
    public static final int GENSEE_SUB_STATUS_HAS = 1;
    /** 是否已经通知 0未通知 */
    public static final int IS_NOTICE_NO = 0;
    /** 是否已经通知 1已通知 */
    public static final int IS_NOTICE_YES = 1;


    private GenseeWebCast genseeWebCast; // 直播

    public GenseeWebCast getGenseeWebCast() {
        return genseeWebCast;
    }


    public void setGenseeWebCast(GenseeWebCast genseeWebCast) {
        this.genseeWebCast = genseeWebCast;
    }


}
