package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CoursewareNoteEntity;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CoursewareNote extends CoursewareNoteEntity {

    private static final long serialVersionUID = 2288181044995149860L;

    /**常量：课件笔记当前版本*/
    public static final Integer CURRENT_VERSION=1;

    /**常量：非课件笔记当前版本*/
    public static final Integer NOT_CURRENT_VERSION=0;

    /**课程名称（回显字段|条件查询）*/
    private String courseName;

    /**章节名称（回显字段|条件查询）*/
    private String sectionName;

    /**提交人姓名（回显字段）*/
    private String submitName;

    /**归属机构（回显字段）*/
    private String orgName;

    /**审核状态*/
    private Integer auditStatus;

    /**当前课件笔记的版本集合*/
    private List<CoursewareNoteVersion> noteVersionCollect;

    public String getCourseName() { return courseName; }

    public void setCourseName(String courseName) { this.courseName = courseName; }

    public String getSectionName() { return sectionName; }

    public void setSectionName(String sectionName) { this.sectionName = sectionName; }

    public String getSubmitName() { return submitName; }

    public void setSubmitName(String submitName) { this.submitName = submitName; }

    public String getOrgName() { return orgName; }

    public void setOrgName(String orgName) { this.orgName = orgName; }

    public List<CoursewareNoteVersion> getNoteVersionCollect() { return noteVersionCollect; }

    public void setNoteVersionCollect(List<CoursewareNoteVersion> noteVersionCollect) { this.noteVersionCollect = noteVersionCollect; }

    public Integer getAuditStatus() { return auditStatus; }

    public void setAuditStatus(Integer auditStatus) { this.auditStatus = auditStatus; }

    @Override
    public String toString() {
        return "CoursewareNote{" +
                "courseName='" + courseName + '\'' +
                ", sectionName='" + sectionName + '\'' +
                ", submitName='" + submitName + '\'' +
                ", orgName='" + orgName + '\'' +
                ", auditStatus=" + auditStatus +
                ", noteVersionCollect=" + noteVersionCollect +
                '}';
    }

    public static class NoteSyncingDTO implements Serializable {
        private static final long serialVersionUID = -207728541993539349L;

        /**九天传输：全局唯一 request id,请求方生成 16位字符串*/
        private String id;

        /**九天传输：课程Id（必填）*/
        private String itemid;

        /**九天传输：小节Id（非必填）*/
        private String noduleid;

        /**九天传输：0-课程，1-小节（课程的话，查课程和课程下面所有小节信息，小节的话只查小节）*/
        private String type;

        public String getId() { return id; }

        public void setId(String id) { this.id = id; }

        public String getItemid() { return itemid; }

        public void setItemid(String itemid) { this.itemid = itemid; }

        public String getNoduleid() { return noduleid; }

        public void setNoduleid(String noduleid) { this.noduleid = noduleid; }

        public String getType() { return type; }

        public void setType(String type) { this.type = type; }

        @Override
        public String toString() {
            return "NoteSyncingDTO{" +
                    "id='" + id + '\'' +
                    ", itemid='" + itemid + '\'' +
                    ", noduleid='" + noduleid + '\'' +
                    ", type='" + type + '\'' +
                    '}';
        }
    }
}
