package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.OfflineCourseQuestionnaireChapterEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 线下课程调查问卷章节表
 *
 * <AUTHOR>
 * @date 2021/3/5/0005 15:15
 */
public class OfflineCourseQuestionnaireChapter extends OfflineCourseQuestionnaireChapterEntity {

    private static final long serialVersionUID = -2003015898858963199L;

    public static final String TEMPLATE_INDEX = "序号";
    public static final String TEMPLATE_COURSE_NAME = "课程名称";
    public static final String TEMPLATE_LECTURER_NAME = "讲师名称";

    /**
     * 导入Excel读取结果缓存时间:5min
     */
    public static final int CACHE_TIME = 60 * 5;

    public static final Integer TEMPLATE_COURSE_NAME_COLUMN = 1;
    public static final Integer TEMPLATE_LECTURER_NAME_COLUMN = 2;

    public static final String COURSE_NAME = "课程名称";
    public static final String LECTURER_NAME = "讲师名称";

    public static final String CACHE_KEY_INSERT_LIST = "correctInsertList";
    public static final String CACHE_KEY_ERROR_LIST = "errorList";

    /**
     * 导入限制条数
     */
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;

    /**
     * 行数，导入时辅助
     */
    private int row;

    private List<RowError> errors = new ArrayList<>();

    private List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList;
    private List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForCourse;
    private List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForTeacher;
    private OfflineQuestionnaireAnswer offlineQuestionnaireAnswer;

    /**
     * 课程内容统计
     */
    private Statistics courseContentStatistics;

    /**
     * 讲师水平统计
     */
    private Statistics instructorLevelStatistics;

    /**
     * 章节总统计数
     */
    private String chapterStatisticsNum;

    private int rowIndex;

    private Integer size;





    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }


    public List<QuestionnaireMouldQuestion> getQuestionnaireMouldQuestionList() {
        return questionnaireMouldQuestionList;
    }

    public void setQuestionnaireMouldQuestionList(List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList) {
        this.questionnaireMouldQuestionList = questionnaireMouldQuestionList;
    }

    public OfflineQuestionnaireAnswer getOfflineQuestionnaireAnswer() {
        return offlineQuestionnaireAnswer;
    }

    public void setOfflineQuestionnaireAnswer(OfflineQuestionnaireAnswer offlineQuestionnaireAnswer) {
        this.offlineQuestionnaireAnswer = offlineQuestionnaireAnswer;
    }

    public Statistics getCourseContentStatistics() {
        return courseContentStatistics;
    }

    public void setCourseContentStatistics(Statistics courseContentStatistics) {
        this.courseContentStatistics = courseContentStatistics;
    }

    public Statistics getInstructorLevelStatistics() {
        return instructorLevelStatistics;
    }

    public void setInstructorLevelStatistics(Statistics instructorLevelStatistics) {
        this.instructorLevelStatistics = instructorLevelStatistics;
    }


    public String getChapterStatisticsNum() {
        return chapterStatisticsNum;
    }

    public void setChapterStatisticsNum(String chapterStatisticsNum) {
        this.chapterStatisticsNum = chapterStatisticsNum;
    }

    public List<QuestionnaireMouldQuestion> getQuestionnaireMouldQuestionListForCourse() {
        return questionnaireMouldQuestionListForCourse;
    }

    public void setQuestionnaireMouldQuestionListForCourse(List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForCourse) {
        this.questionnaireMouldQuestionListForCourse = questionnaireMouldQuestionListForCourse;
    }

    public List<QuestionnaireMouldQuestion> getQuestionnaireMouldQuestionListForTeacher() {
        return questionnaireMouldQuestionListForTeacher;
    }

    public void setQuestionnaireMouldQuestionListForTeacher(List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForTeacher) {
        this.questionnaireMouldQuestionListForTeacher = questionnaireMouldQuestionListForTeacher;
    }


    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
