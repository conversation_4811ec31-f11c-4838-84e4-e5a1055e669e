package com.zxy.product.course.content;

/**
 * 限流常量类
 * <AUTHOR>
 * @date 2024年12月12日 17:41
 */
public class CourseLimitConstants {
    /**用户白名单*/
    public static String HumanWhiteMemberIds = "human-white-member-ids";

    /**递减状态 0表示数据未被redis递减 1表示数据已被redis递减*/
    public static Integer NotDecremented=0;
    public static Integer Decremented=1;

    /**限流资源是否核心*/
    public static Integer Ordinary=0;
    public static Integer Core=1;

    /**限流规则开关*/
    public static Integer FlowLimitDisable=0;
    public static Integer FlowLimitEnable=1;

    /**redisKey相关存储*/
    public static String CacheKeyApplication = "course-study";
    public static String CacheKeyModule = "limit-num";
    public static String CacheKeyOfSystem="system";
    public static String CacheKeyRuleConfigSuffix="rule-config-key-FLOW_LIMIT_CONFIG";
    public static String CacheKeyCoreOnline="flow-limit-max-online-core";
    public static String CacheKeyOrdinaryOnline="flow-limit-max-online-ordinary";
    public static Integer CacheKeyExpireOnline=60*60*12;

    /**默认令牌||令牌续期时间*/
    public static Long DefaultTokenTime=12*60*1000L;

    /**课程最大限流阈值*/
    public static Long DefaultCourseOnline=70000L;
}
