package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GenseeWebCastEntity;

import java.util.List;

/**
 * Created by <PERSON> on 2017/2/15.
 */
public class GenseeWebCast extends GenseeWebCastEntity {
    private static final long serialVersionUID = 8823460170794881474L;

    public static final String HMAC_SHA256 = "HmacSHA256";

    // 与展示互动接口用户操作行为对应码解释
    /**
     * 101—用户进入(注：只要用户成功加入到直播中，就触发)
     */
    public static final String GENSEEWEBCAST_ACTION_USER_ENTER = "101";
    /**
     * 103—直播开始(注:组织者点击开始直播按钮触发)
     */
    public static final String GENSEEWEBCAST_ACTION_START = "103";
    /**
     * 104暂停直播(上课)(注:组织者点击暂停直播触发)
     */
    public static final String GENSEEWEBCAST_ACTION_PAUSE = "104";
    /**
     * 110—用户异常离开（注：用户由于网络中断、或者程序死或者死机等，或触发）
     */
    public static final String GENSEEWEBCAST_ACTION_ABNORMAL_EXIT = "110";
    /**
     * 105—停止直播(上课)(注:组织者关闭直播,或者会议里没有任何的客户端超过 5 分钟,就会触发)
     */
    public static final String GENSEEWEBCAST_ACTION_ABNORMAL_CLOSE = "105";
    /**
     * 107—退出直播教室（注：用户手动关闭客户端或播放器，触发）
     */
    public static final String GENSEEWEBCAST_ACTION_EXIT = "107";

    public static final String GENSEEWEBCAST_ACTION_CREATE = "201";// 201 直播创建成功
    public static final String GENSEEWEBCAST_ACTION_UPDATE = "202";// 202 直播修改成功
    public static final String GENSEEWEBCAST_ACTION_DELETE = "203";// 203 直播删除成功
    public static final String GENSEEWEBCAST_ACTION_HISTORY_WRITE = "204";// 204 直播访问记录回调

    public static final Integer GENSEE_TYPE_WEBCAST = 0; // webcast
    public static final Integer GENSEE_TYPE_TRAINING = 1; // training

    //消息发送
    public static final String TEMPLATE_CODE_MARK_PAPER = "live_publish";

    //邮件
    public static final String MESSAGE_TYPE_EMAIL = "1";
    //站内信
    public static final String MESSAGE_TYPE_INNER = "0";
    //app推送
    public static final String MESSAGE_TYPE_APP = "3";
    //短信
    public static final String MESSAGE_TYPE_SHORT = "2";

    public static final Integer SCENE_TWO = 2;//咪咕直播
    public static final Integer SCENE_ONE = 1;//云视讯直播
    public static final Integer SCENE_ZERO = 0;//普通直播
    public static final Integer THREE_HUNDRED = 300;
    public static final Integer MAX_PLAN_NUMBER = 99999;
    public static final String GRANT_TYPE = "ext1.0";
    public static final String GET_AUTH_BODY = "%5B%5D";
    public static final Integer MIGU_RESPONSE_CODE_SUCCESS = 0;

    public static final Long ONE_DAY_MILLIS = (long) 1000 * 60 * 60 * 24;

    public static final Integer PUBLIC_LIVING = 1;//公开直播
    public static final Integer NON_PUBLIC_LIVING = 0;//非公开直播

    public static final String ROOT_ORGANIZATION_ID = "1";

    public static final String MIGU_CREAT_LIVING_URL = "/v1/room/createLive";
    public static final String MIGU_LIVING_ROOM_INFO_URL = "/v1/room/getLiveRoomInfo";
    public static final String MIGU_LIVING_CHANNEL_INFO_URL = "/v1/room/getLiveChannelInfo";
    public static final String MIGU_LIVING_SAVE_WATCH_AUTH_URL = "/v1/room/saveWatchAuth";
    public static final String MIGU_LIVING_DELETE_URL = "/v1/room/deleteLive";
    public static final String MIGU_SIGN_INFO_URL = "/v1/room/signConfig/listByPage";
    public static final String MIGU_START_LIVING_URL = "/v1/room/startLive";
    public static final String MIGU_STOP_LIVING_URL = "/v1/room/stopLive";
    public static final String MIGU_LIVING_UPDATE_URL = "/v1/room/updateLive";
    public static final String MIGU_LIVING_RESUME_PREVIEW_URL = "/v1/room/resumeLivePreview";
    public static final String MIGU_LIVING_SSO_TICKET_URL = "/v1/user/getSsoTicket";
    public static final String MIGU_LIVING_EDU_SSO_TICKET_URL = "/v1/user/getEduSsoTicket";
    public static final String MIGU_LIVING_COUNT_DOWN_URL= "/v1/room/configLiveCountDown";
    
    public static final String MIGU_LIST_BY_PAGE_URL= "/v1/room/signConfig/listByPageInfo";


    //讲师
    public static final Integer CODE_TYPE_LECTURER = 1;
    //嘉宾
    public static final Integer CODE_TYPE_GUEST = 2;
    //助教
    public static final Integer CODE_TYPE_TEACHING_ASSISTANT = 3;
    //拉流
    public static final Integer MIGU_PULL_STREAM = 1;
    //推流
    public static final Integer MIGU_PUSH_STREAM = 0;
    public static final String MIGU_LIVING_STATUS_START = "QYZB_LIVE_START";
    public static final String MIGU_LIVING_STATUS_END = "QYZB_LIVE_END";
    public static final Integer PAGE_SIZE = 500;
    public static final Integer RANDOM_STR_LENGTH = 8;
    public static final Integer PASSWORD_PERMISSION = 1;
    public static final Integer PUBLIC_PERMISSION = 0;
    public static final Integer LOOK_BACK = 1;
    public static final Integer NON_LOOK_BACK = 0;
    public static final Integer IN_HOUSE_LECTURER = 0; //内部讲师

    //公开观看
    public static final int PUBLIC_WATCH = 1;
    //密码观看
    public static final int PWD_WATCH = 2;
    //三方鉴权
    public static final int AUTHENTICATION_WATCH = 3;
    public static final int START_LIVING = 1;
    public static final int STOP_LIVING = 2;
    //咪咕分配的PLATFORM
    public static final String PLATFORM = "ZYWD";
    public static final Integer REVIEW_COURSE_NUMBER =0;
    public static final Integer START_COUNT_DOWN = 1;


    /**
     * 直播状态  0 未发布
     */
    public static final Integer STATUS_NOT_PUBLISH = 0;
    /**
     * 直播状态  1 未开始
     */
    public static final Integer STATUS_NOT_START = 1;
    /**
     * 直播状态  2 进行中
     */
    public static final Integer STATUS_RUNNING = 2;
    /**
     * 直播状态  3 已结束
     */
    public static final Integer STATUS_FINISH = 3;
    /**
     * 直播状态  4 已撤销
     */
    public static final Integer STATUS_CANCEL = 4;

    /**
     * 云中心创建/修改直播间请求状态  0 等待发送请求
     */
    public static final Integer GENSEE_STATUS_REQUESTING = 0;
    /**
     * 云中心创建/修改直播间请求状态  1 请求发送成功，等待中
     */
    public static final Integer GENSEE_STATUS_REQUEST_SEND_SUCCESSED = 1;
    /**
     * 云中心创建/修改直播间请求状态  2 请求发送失败
     */
    public static final Integer GENSEE_STATUS_REQUEST_SEND_FAILED = 2;
    /**
     * 云中心创建/修改直播间请求状态  3 请求成功
     */
    public static final Integer GENSEE_STATUS_REQUEST_SUCCESSED = 3;
    /**
     * 云中心创建/修改直播间请求状态  4 请求失败
     */
    public static final Integer GENSEE_STATUS_REQUEST_FAILED = 4;

    /**
     * 活动首页 查询状态  1： 正在进行
     */
    public static final Integer ACTIVITY_HOME_STATUS_RUNNING = 1;
    /**
     * 活动首页 查询状态  2： 即将开始
     */
    public static final Integer ACTIVITY_HOME_STATUS_NOT_START = 2;
    /**
     * 活动首页 查询状态  3： 往期回顾
     */
    public static final Integer ACTIVITY_HOME_STATUS_FINISH = 3;
    /**
     * 个人中心-我的直播 4：已撤销
     */
    public static final Integer ACTIVITY_HOME_STATUS_CANCEL = 4;

    public static final Integer RELATIVE_GENSEES_LIMIT_DEFAULT = 3; // 相关直播最大查询个数
    public static final Integer RELATIVE_COURSES_LIMIT_DEFAULT = 4; // 相关直播最大查询个数

    // 调用云视讯创建会议接口，比选择时间要提前半小时
    public static final Long HALF_HOUR = 30 * 60 * 1000L;
    public static final Long AN_HOUR = 60L;
    public static final String KEY = "ff756d29879a8e08a63552adc7ab79bb";// key
    public static final String IDENTITY = "e34e013dbfef23f20cc6d79846b527d8";// 厂商标识
    public static final String ECID = "100A23100095097565"; //企业id
    public static final int YSX_STATUS = 1; //研讨会

    private String publishClient;//自适应终端
    private String organizationName;//归属部门
    private Organization organization;
    private Organization releaseOrg;    //  发布部门
    private Member releaseUser; //  发布人
    private List<AudienceItem> audienceItems;
    private List<BusinessTopic> businessTopics; // 相关话题
    private List<GenseeLecturer> lecturers; // 讲师
    private Member currentUser;
    private CourseScore courseScore; // 评分
    private Integer subStatus;//订阅状态（0：未订阅,1：已订阅）
    private List<GenseeWebCast> relatedGensee;
    private boolean allowPublish = false;
    private String lecturerNames;//讲师名
    private Long joinTime;//进入直播时间
    private List<GenseeBusiness> businesses;
    private Long uid;//传给展视互动的用户id
    private Long serverTime;//服务器时间
    private List<CourseAttachment> genseeAttachments;
    private String hostName;//用于云视讯主持人姓名
    //收藏数
    private Integer collectNum;
    //评论数
    private Integer commentNum;
    //相关话题名称
    private List<String> businessTopicNames;
    //专家工作室名称
    private String studioName;
    //专家工作室头像id
    private String studioPortraitId;
    //专家工作室头像地址
    private String studioPortraitPath;
    //直播收藏id
    private String collectId;
    //发布人名字
    private String releaseUserName;
    /**
     * 直播开始时间(yyyy-MM-dd)格式
     */
    private String startTimeStr;
    //返回当前时间
    private Long nowTime;

    /**
     * PC首页 提供给前端 是否显示startTimeStr
     */
    private boolean pcShowStartTime;

    /**
     * 是否是党校(目前只能根据组织名称中是否包含"党校"来判断)
     */
    private boolean partyStatus;
    //是否审核状态 （0：不需要审核 1：需要审核）
    private Integer reviewStatus;

    //审核状态
    private Integer auditStatus;

    private boolean reviewPass;

    //直播学习时长
    private Integer studyTime;
    private LiveVirtualSpace liveVirtualSpace;

    private Integer liveStatus;

    public Integer getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(Integer liveStatus) {
        this.liveStatus = liveStatus;
    }

    public LiveVirtualSpace getLiveVirtualSpace() {
        return liveVirtualSpace;
    }

    public void setLiveVirtualSpace(LiveVirtualSpace liveVirtualSpace) {
        this.liveVirtualSpace = liveVirtualSpace;
    }

    public boolean isAllowPublish() {
        return allowPublish;
    }

    public void setAllowPublish(boolean allowPublish) {
        this.allowPublish = allowPublish;
    }

    public Organization getReleaseOrg() {
        return releaseOrg;
    }

    public void setReleaseOrg(Organization releaseOrg) {
        this.releaseOrg = releaseOrg;
    }

    public Member getReleaseUser() {
        return releaseUser;
    }

    public void setReleaseUser(Member releaseUser) {
        this.releaseUser = releaseUser;
    }

    public List<AudienceItem> getAudienceItems() {
        return audienceItems;
    }

    public void setAudienceItems(List<AudienceItem> audienceItems) {
        this.audienceItems = audienceItems;
    }

    public List<BusinessTopic> getBusinessTopics() {
        return businessTopics;
    }

    public void setBusinessTopics(List<BusinessTopic> businessTopics) {
        this.businessTopics = businessTopics;
    }

    public List<GenseeLecturer> getLecturers() {
        return lecturers;
    }

    public void setLecturers(List<GenseeLecturer> lecturers) {
        this.lecturers = lecturers;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<GenseeBusiness> getBusinesses() {
        return businesses;
    }

    public void setBusinesses(List<GenseeBusiness> businesses) {
        this.businesses = businesses;
    }

    public Member getCurrentUser() {
        return currentUser;
    }

    public void setCurrentUser(Member currentUser) {
        this.currentUser = currentUser;
    }

    public Integer getSubStatus() {
        return subStatus;
    }

    public void setSubStatus(Integer subStatus) {
        this.subStatus = subStatus;
    }

    public List<GenseeWebCast> getRelatedGensee() {
        return relatedGensee;
    }

    public void setRelatedGensee(List<GenseeWebCast> relatedGensee) {
        this.relatedGensee = relatedGensee;
    }

    public String getLecturerNames() {
        return lecturerNames;
    }

    public void setLecturerNames(String lecturerNames) {
        this.lecturerNames = lecturerNames;
    }

    public Long getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(Long joinTime) {
        this.joinTime = joinTime;
    }

    public CourseScore getCourseScore() {
        return courseScore;
    }

    public void setCourseScore(CourseScore courseScore) {
        this.courseScore = courseScore;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getServerTime() {
        return serverTime;
    }

    public void setServerTime(Long serverTime) {
        this.serverTime = serverTime;
    }

    public List<CourseAttachment> getGenseeAttachments() {
        return genseeAttachments;
    }

    public void setGenseeAttachments(List<CourseAttachment> genseeAttachments) {
        this.genseeAttachments = genseeAttachments;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getPublishClient() {
        return publishClient;
    }

    public void setPublishClient(String publishClient) {
        this.publishClient = publishClient;
    }

    public Integer getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Integer collectNum) {
        this.collectNum = collectNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public List<String> getBusinessTopicNames() {
        return businessTopicNames;
    }

    public void setBusinessTopicNames(List<String> businessTopicNames) {
        this.businessTopicNames = businessTopicNames;
    }

    public String getStudioName() {
        return studioName;
    }

    public void setStudioName(String studioName) {
        this.studioName = studioName;
    }

    public String getStudioPortraitId() {
        return studioPortraitId;
    }

    public void setStudioPortraitId(String studioPortraitId) {
        this.studioPortraitId = studioPortraitId;
    }

    public String getStudioPortraitPath() {
        return studioPortraitPath;
    }

    public void setStudioPortraitPath(String studioPortraitPath) {
        this.studioPortraitPath = studioPortraitPath;
    }

    public String getCollectId() {
        return collectId;
    }

    public void setCollectId(String collectId) {
        this.collectId = collectId;
    }

    public String getReleaseUserName() {
        return releaseUserName;
    }

    public void setReleaseUserName(String releaseUserName) {
        this.releaseUserName = releaseUserName;
    }

    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getNowTime() {
        return nowTime;
    }

    public void setNowTime(Long nowTime) {
        this.nowTime = nowTime;
    }

    public boolean isPcShowStartTime() {
        return pcShowStartTime;
    }

    public void setPcShowStartTime(boolean pcShowStartTime) {
        this.pcShowStartTime = pcShowStartTime;
    }

    public boolean isPartyStatus() {
        return partyStatus;
    }

    public void setPartyStatus(boolean partyStatus) {
        this.partyStatus = partyStatus;
    }

    public boolean isReviewPass() {
        return reviewPass;
    }

    public void setReviewPass(boolean reviewPass) {
        this.reviewPass = reviewPass;
    }

    public Integer getStudyTime() {
        return studyTime;
    }

    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

}
