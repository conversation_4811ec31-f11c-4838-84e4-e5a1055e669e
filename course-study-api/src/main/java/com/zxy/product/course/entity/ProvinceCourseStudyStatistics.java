package com.zxy.product.course.entity;

import java.io.Serializable;

/**
 * @ClassName : ProvinceCourseStudyStatistics
 * @Description : 省公司学习数据统计
 */
public class ProvinceCourseStudyStatistics implements Serializable {


    private static final long serialVersionUID = -8207504835696585383L;

    private long    number;                 //学习人数
    private long    totalLengthTime;        //学习总时长 h
    private double  avgLengthTime;          //人均时长
    private long    memberTime;             //登录人次
    private long    memberNumber;           //登录人数
    private double  activityLoginRadio;     //登录活跃度
    private String  name;                   //组织名称
    private String  orgShortName;           //组织名称
    private String  orgId;                  //组织ID
    private int loginPopularity;            // 登录热度

    public ProvinceCourseStudyStatistics() {}

    public ProvinceCourseStudyStatistics(long number, long totalLengthTime) {
        this.number = number;
        this.totalLengthTime = totalLengthTime;
    }

    public ProvinceCourseStudyStatistics(long number, long totalLengthTime, double avgLengthTime) {
        this.number = number;
        this.totalLengthTime = totalLengthTime;
        this.avgLengthTime = avgLengthTime;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public long getTotalLengthTime() {
        return totalLengthTime;
    }

    public void setTotalLengthTime(long totalLengthTime) {
        this.totalLengthTime = totalLengthTime;
    }

    public double getAvgLengthTime() {
        return avgLengthTime;
    }

    public void setAvgLengthTime(double avgLengthTime) {
        this.avgLengthTime = avgLengthTime;
    }

    public long getMemberTime() {
        return memberTime;
    }

    public void setMemberTime(long memberTime) {
        this.memberTime = memberTime;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgShortName() {
        return orgShortName;
    }

    public void setOrgShortName(String orgShortName) {
        this.orgShortName = orgShortName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public long getMemberNumber() {
        return memberNumber;
    }

    public void setMemberNumber(long memberNumber) {
        this.memberNumber = memberNumber;
    }

    public double getActivityLoginRadio() {
        return activityLoginRadio;
    }

    public void setActivityLoginRadio(double activityLoginRadio) {
        this.activityLoginRadio = activityLoginRadio;
    }

    public int getLoginPopularity() {
        return loginPopularity;
    }

    public void setLoginPopularity(int loginPopularity) {
        this.loginPopularity = loginPopularity;
    }
}


