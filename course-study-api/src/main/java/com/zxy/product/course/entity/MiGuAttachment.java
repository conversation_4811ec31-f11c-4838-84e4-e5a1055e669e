package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.MiguAttachmentEntity;

/**
 *  自定义返回类
 */
public class MiGuAttachment extends MiguAttachmentEntity {


    private static final long serialVersionUID = 6391609669997975357L;
    private String orgName;
    private String subjectName;
    private String submitUserName;
    private String reviewUserName;
    private String orgId;

    public static final Integer REVIEW_PASS = 2;
    public static final Integer IN_REVIEW = 1;
    public static final Integer REVIEW_NOPASS = 0;
    public static final Integer NO_UPLOAD = 3;

    public static final Integer LOOK_BACK = 0;
    public static final Integer RECORD_DOWNLOAD = 1;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }


    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
