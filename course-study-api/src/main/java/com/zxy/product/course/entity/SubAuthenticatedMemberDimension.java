package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedMemberDimensionEntity;


/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class SubAuthenticatedMemberDimension extends SubAuthenticatedMemberDimensionEntity {
    private static final long serialVersionUID = -6230083454508749613L;
    public static final int TEMPLATE_MEMBER_NAME = 0;
    public static final int TEMPLATE_MEMBER_CODE = 1;
    //public static final int TEMPLATE_ONE = 2;
    //public static final int TEMPLATE_TWO = 3;
    //public static final int TEMPLATE_THREE = 4;
    //public static final int TEMPLATE_FOUR = 5;
    //public static final int TEMPLATE_FIVE = 6;
    //public static final int TEMPLATE_SIX = 7;
    //public static final int TEMPLATE_EIGHT = 9;
    //public static final int TEMPLATE_SEVEN = 8;
    //public static final int TEMPLATE_NINE = 10;
    //public static final int TEMPLATE_TEN = 11;


    public static final int IS_LIGHTED = 1;
    public static final int NOT_LIGHTED = 0;

    private SubAuthenticatedDimension subAuthenticatedDimension;
    private String subAuthenticatedId;

    public String getSubAuthenticatedId() {
        return subAuthenticatedId;
    }

    public void setSubAuthenticatedId(String subAuthenticatedId) {
        this.subAuthenticatedId = subAuthenticatedId;
    }

    public SubAuthenticatedDimension getSubAuthenticatedDimension() {
        return subAuthenticatedDimension;
    }

    public void setSubAuthenticatedDimension(SubAuthenticatedDimension subAuthenticatedDimension) {
        this.subAuthenticatedDimension = subAuthenticatedDimension;
    }
}
