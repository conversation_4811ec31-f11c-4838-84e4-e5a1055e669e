package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.ReportRedlistEntity;

import java.io.Serializable;

public class ResourceVisit  extends ReportRedlistEntity implements Serializable{


    private static final long serialVersionUID = 4594722506272157745L;

    private String contentName;
    private Integer visit;
    private String contentId;
    private Integer contentType;
    private Integer day;
    private String organizationId;
    private String organizationName;

    public ResourceVisit() {
    }

    public ResourceVisit(String contentName, Integer visit) {
        this.contentName = contentName;
        this.visit = visit;
    }

    public String getContentName() {
        return contentName;
    }

    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    public Integer getVisit() {
        return visit;
    }

    public void setVisit(Integer visit) {
        this.visit = visit;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
}
