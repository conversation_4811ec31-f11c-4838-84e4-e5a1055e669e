package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.StudyActivityConfigEntity;

/**
 * <AUTHOR> zhouyong
 */
public class StudyActivityConfig extends StudyActivityConfigEntity {

    private static final long    serialVersionUID   = -8693186079294303443L;

    public static final Integer STUDY_COURSE_TYPE  = 1;          //课程
    public static final Integer STUDY_SUBJECT_TYPE = 2;          //专题

    public static final Integer UNPUBLISHED        = 0;          //未发布
    public static final Integer PUBLISHED          = 1;          //已发布

    public static final String   UP                = "up";       //上移
    public static final String   DOWN              = "down";     //下移

    private boolean first;      //已发布的活动是否是第一条
    private boolean last;       //已发布的活动是否是最后一条

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }

    public boolean isLast() {
        return last;
    }

    public void setLast(boolean last) {
        this.last = last;
    }
}
