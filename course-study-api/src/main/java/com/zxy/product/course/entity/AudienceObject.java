package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AudienceObjectEntity;

public class AudienceObject extends AudienceObjectEntity {

    private static final long serialVersionUID = 6475487201287025917L;

    public static final Integer BUSINESS_TYPE_COURSE = 1; // 课程
    public static final Integer BUSINESS_TYPE_SUBJECT = 2; // 专题
    public static final Integer BUSINESS_TYPE_PUSH = 3; //课程推送
    public static final Integer BUSINESS_TYPE_SHARE = 4; //分享
    public static final Integer BUSINESS_TYPE_KNOWLEDGE = 5; //知识
    public static final Integer BUSINESS_TYPE_GENSEE = 6; //直播
    public static final Integer BUSINESS_TYPE_COCKPIT = 7; //驾驶舱
    public static final Integer BUSINESS_TYPE_SUBJECT_STUDY_PLAN = 8; //专题学习计划
    public static final Integer BUSINESS_TYPE_COURSE_STUDY_PLAN = 9; //课程学习计划
    public static final Integer BUSINESS_TYPE_AUTHENTICATED_ZONE = 10; // 认证专区
    public static final Integer BUSINESS_TYPE_AUTHENTICATED_ZONE_GROUP = 11; // 认证专区下的组

    //是否推送成功
    public static final Integer PUSH_DEFULT = 0;//默认 否
    public static final Integer PUSH_TRUE = 1;//推送成功

    private String memberId;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }
}
