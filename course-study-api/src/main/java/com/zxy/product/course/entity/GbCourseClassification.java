package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.GbCourseClassificationEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/8/16
 * @description ：高标党建
 */
public class GbCourseClassification extends GbCourseClassificationEntity {
    private static final long serialVersionUID = -4349712686617959843L;

    GbCourseMiddle gbCourseMiddle;
    GbCourseLibrary gbCourseLibrary;
    GbLecturerLibrary gbLecturerLibrary;

    List<GbCourseLibrary> gbCourseLibraryList;
    List<GbLecturerLibrary> gbLecturerLibraryList;
    List<GbCourseMiddle> gbCourseMiddleList;

    public GbCourseMiddle getGbCourseMiddle() {
        return gbCourseMiddle;
    }

    public void setGbCourseMiddle(GbCourseMiddle gbCourseMiddle) {
        this.gbCourseMiddle = gbCourseMiddle;
    }

    public GbCourseLibrary getGbCourseLibrary() {
        return gbCourseLibrary;
    }

    public void setGbCourseLibrary(GbCourseLibrary gbCourseLibrary) {
        this.gbCourseLibrary = gbCourseLibrary;
    }

    public GbLecturerLibrary getGbLecturerLibrary() {
        return gbLecturerLibrary;
    }

    public void setGbLecturerLibrary(GbLecturerLibrary gbLecturerLibrary) {
        this.gbLecturerLibrary = gbLecturerLibrary;
    }

    public List<GbCourseLibrary> getGbCourseLibraryList() {
        return gbCourseLibraryList;
    }

    public void setGbCourseLibraryList(List<GbCourseLibrary> gbCourseLibraryList) {
        this.gbCourseLibraryList = gbCourseLibraryList;
    }

    public List<GbLecturerLibrary> getGbLecturerLibraryList() {
        return gbLecturerLibraryList;
    }

    public void setGbLecturerLibraryList(List<GbLecturerLibrary> gbLecturerLibraryList) {
        this.gbLecturerLibraryList = gbLecturerLibraryList;
    }

    public List<GbCourseMiddle> getGbCourseMiddleList() {
        return gbCourseMiddleList;
    }

    public void setGbCourseMiddleList(List<GbCourseMiddle> gbCourseMiddleList) {
        this.gbCourseMiddleList = gbCourseMiddleList;
    }
}
