package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseCategoryEntity;

/**
 * Created by ch<PERSON><PERSON> on 16/11/7.
 */
public class CourseCategory extends CourseCategoryEntity {

    private static final long serialVersionUID = -340385233881532868L;

    public static final String COURSE_CATEGORY_URI = "course-study/course-category";

    public static final Integer STATE_ENABLE = 1;// 状态:启用
    public static final Integer STATE_DISABLE = 2;// 状态:禁用

    public static final int HIDE_NO = 0;
    public static final int HIDE_YES = 1;
    
    public static final String COURSE_CATEGORY_CACH_KEY = "course-category-front-cache-key";


    // 父目录
    private CourseCategory parent;

    private Organization organization;

    private Integer courseCount;
    
    private Integer Index;

    public Integer getIndex() {
		return Index;
	}

	public void setIndex(Integer index) {
		Index = index;
	}

	public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }


    public CourseCategory getParent() {
        return parent;
    }

    public void setParent(CourseCategory parent) {
        this.parent = parent;
    }

    public Integer getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(Integer courseCount) {
        this.courseCount = courseCount;
    }
}
