package com.zxy.product.course.content.knowledge.graph;

import java.util.HashMap;
import java.util.Map;

/**
 * 课程Or专题类型枚举
 * <AUTHOR>
 * @date 2023年10月20日 9:45
 */
public enum CourseTypeEnum {
    /**混合类课程Or专题类型*/
    MixedClasses(0,"混合类"),
    /**文档课程Or专题类型*/
    Documentation(1,"文档"),
    /**图片课程Or专题类型*/
    Image(2,"图片"),
    /**URL课程Or专题类型*/
    Url(3,"URL"),
    /**Scrom课程Or专题类型*/
    Scrom(4,"scrom"),
    /**音频类课程Or专题类型*/
    Audio(5,"音频类"),
    /**音频类课程Or专题类型*/
    Video(6,"视频类"),
    /**电子书课程Or专题类型*/
    EBooks(7,"电子书"),
    /**任务类课程Or专题类型*/
    Task(8,"任务"),
    /**考试类课程Or专题类型*/
    Exam(9,"考试"),
    /**课程类课程Or专题类型*/
    Course(10,"课程"),
    /**面授类课程Or专题类型*/
    FaceToFace(11,"面授"),
    /**调研类课程Or专题类型*/
    Research(12,"调研"),
    /**评估类课程Or专题类型*/
    Assess(13,"评估"),
    ;

    private Integer code;
    private String value;

    CourseTypeEnum(int code, String value) {
        this.code=code;
        this.value=value;
    }

    public static Map<Integer, String> courseTypeMap = new HashMap<>(18);

    static {
        for (CourseTypeEnum type : values()) {
            courseTypeMap.put(type.getCode(), type.getValue());
        }
    }

    public Integer getCode() { return code; }

    public void setCode(Integer code) { this.code = code; }

    public String getValue() { return value; }

    public void setValue(String value) { this.value = value; }
}
